
string Ver = "0.9.102";

// AutoPillock/Command script (c)cheerkin

Dictionary<string, ǂ> customIcons = new Dictionary<string, ǂ>() {
	{ "wait-for-signal", new ǂ(@"Sprites/default=ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01422691,PosY=-0.4898785,Rotation=-1.05,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01869535,PosY=0.4797863,Rotation=-1.05,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE")},
	{ "wait-10-s", new ǂ(@"Sprites/default=ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01422691,PosY=-0.4898785,Rotation=-1.05,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01869535,PosY=0.4797863,Rotation=-1.05,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE")},
	{ "attack-pos", new ǂ(@"Sprites/default=ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=0.4274551,Rotation=-6.250005,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=-0.4617766,Rotation=-9.450017,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=0.4543742,PosY=-0.02162927,Rotation=-7.900011,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.4750742,PosY=0.03422689,Rotation=-4.799999,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=CircleHollow,SizeX=0.8046875,SizeY=0.8046875,PosX=-0.02152109,PosY=-0.01269257,Rotation=-3.049997,Type=TEXTURE")},
	{ "attack", new ǂ(@"Sprites/default=ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=0.4274551,Rotation=-6.250005,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=-0.4617766,Rotation=-9.450017,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=0.4543742,PosY=-0.02162927,Rotation=-7.900011,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.4750742,PosY=0.03422689,Rotation=-4.799999,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=CircleHollow,SizeX=0.8046875,SizeY=0.8046875,PosX=-0.02152109,PosY=-0.01269257,Rotation=-3.049997,Type=TEXTURE")},
	{ "ram", new ǂ(@"Sprites/default=ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.8515625,SizeY=0.2754273,PosX=-0.01034999,PosY=-0.03056604,Rotation=5.450003,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.3496094,SizeY=0.1130769,PosX=-0.6672196,PosY=0.2822293,Rotation=5.450003,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.3496094,SizeY=0.1130769,PosX=-0.2427121,PosY=0.6531148,Rotation=5.450003,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=RightTriangle,SizeX=0.2167969,SizeY=0.2167969,PosX=0.4856539,PosY=0.1280661,Rotation=7.000009,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=RightTriangle,SizeX=0.2167969,SizeY=0.2167969,PosX=-0.2181348,PosY=-0.4707129,Rotation=5.400002,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=Circle,SizeX=0.2207031,SizeY=0.2207031,PosX=0.5772582,PosY=-0.6807323,Rotation=7.25001,Type=TEXTURE")},
	{ "jab", new ǂ(@"Sprites/default=ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Textures\FactionLogo\Others\OtherIcon_4.dds,SizeX=1,SizeY=1,PosX=0.3091477,PosY=-0.2897394,Rotation=7.100009,Type=TEXTURE|ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.09765625,SizeY=0.242833,PosX=-0.83032,PosY=0.3112738,Rotation=4.349998,Type=TEXTURE|ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.09765625,SizeY=0.242833,PosX=-0.358893,PosY=0.8005747,Rotation=3.599998,Type=TEXTURE|ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1210938,SizeY=0.3011129,PosX=-0.6694538,PosY=0.6352402,Rotation=3.949997,Type=TEXTURE")},
	{ "su launch", new ǂ(@"Sprites/default=ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=Textures\FactionLogo\Others\OtherIcon_22.dds,SizeX=0.7871094,SizeY=0.7871094,PosX=0.01869488,PosY=0.2005055,Rotation=6.600007,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=-0.7119048,PosY=-0.4049762,Rotation=19.49999,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=-0.296334,PosY=-0.6931946,Rotation=20.09998,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=0.4052203,PosY=-0.6708521,Rotation=20.84997,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=0.7694031,PosY=-0.3826336,Rotation=21.34996,Type=TEXTURE")},
	{ "dock", new ǂ(@"Sprites/default=ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.6035156,SizeY=0.4225572,PosX=0.01869571,PosY=0.5123563,Rotation=3.149998,Type=TEXTURE|ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.3984375,SizeY=0.2789699,PosX=0.03433549,PosY=0.06550622,Rotation=3.149998,Type=TEXTURE|ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.1640625,SizeY=0.1148699,PosX=-0.3812355,PosY=0.03869534,Rotation=2.299999,Type=TEXTURE|ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.1640625,SizeY=0.1148699,PosX=0.4275628,PosY=0.036461,Rotation=3.999997,Type=TEXTURE|ColorR=0,ColorG=215,ColorB=40,ColorA=255,Brush=SquareSimple,SizeX=0.390625,SizeY=0.03775207,PosX=0.0321008,PosY=-0.1288738,Rotation=6.300006,Type=TEXTURE|ColorR=0,ColorG=218,ColorB=37,ColorA=255,Brush=RightTriangle,SizeX=0.4140625,SizeY=0.4140625,PosX=0.05444336,PosY=-0.8974556,Rotation=5.500003,Type=TEXTURE")},
	{ "follow", new ǂ(@"Sprites/default=ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4199219,SizeY=0.4199219,PosX=0.009758592,PosY=-0.2696313,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.6398172,PosY=0.02082145,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.3270218,PosY=0.3403192,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.02092969,PosY=0.6419433,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=-0.6091293,PosY=0.04316401,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=-0.3052711,PosY=0.3425535,Rotation=-0.8000001,Type=TEXTURE")},
	{ "repeat", new ǂ(@"Sprites/default=ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=CircleHollow,SizeX=0.9453125,SizeY=0.9453125,PosX=0.01375592,PosY=-0.005937517,Rotation=0,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=Triangle,SizeX=0.2265625,SizeY=0.2265625,PosX=0.5028293,PosY=0.2001027,Rotation=2.599999,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=Triangle,SizeX=0.2265625,SizeY=0.2265625,PosX=-0.512777,PosY=0.002440572,Rotation=3.649998,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=SquareSimple,SizeX=0.2167969,SizeY=0.08215707,PosX=-0.3006274,PosY=0.182839,Rotation=7.25001,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=SquareSimple,SizeX=0.2167969,SizeY=0.08215707,PosX=0.2771699,PosY=-0.1750945,Rotation=10.45002,Type=TEXTURE")},
	{ "land", new ǂ(@"Sprites/default=ColorR=255,ColorG=255,ColorB=255,ColorA=255,Brush=SquareSimple,SizeX=0.9140625,SizeY=0.2785904,PosX=0.007524252,PosY=0.6374747,Rotation=15.70004,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.6425781,SizeY=0.6425781,PosX=0.02763247,PosY=-0.03503478,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.6113281,SizeY=0.6113281,PosX=0.02316403,PosY=-0.03503478,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.5839844,SizeY=0.5839844,PosX=0.02316403,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.5644531,SizeY=0.5644531,PosX=0.01869571,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.5351563,SizeY=0.5351563,PosX=0.01869571,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.4609375,SizeY=0.4609375,PosX=0.01869571,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=171,ColorG=171,ColorB=171,ColorA=255,Brush=Triangle,SizeX=0.3457031,SizeY=0.3457031,PosX=0.1482821,PosY=0.03199255,Rotation=19.89998,Type=TEXTURE|ColorR=171,ColorG=171,ColorB=171,ColorA=255,Brush=SquareSimple,SizeX=0.1992188,SizeY=0.06837877,PosX=0.02092969,PosY=-0.2651625,Rotation=23.54993,Type=TEXTURE")},
	//{ "task kind", new CustomIcon(@"Orbital Painter normalized export (command:export)")},
	{ "move", new ǂ(@"Sprites/default=ColorR=93,ColorG=113,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.7148438,SizeY=0.7148438,PosX=0.2622281,PosY=-0.00598985,Rotation=1.05,Type=TEXTURE|ColorR=93,ColorG=113,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.40625,SizeY=0.40625,PosX=0.003055453,PosY=-0.5824262,Rotation=0,Type=TEXTURE")}
};
//

Dictionary<string, string> tasksMapping = new Dictionary<string, string>()
	{
		{ "jab", "command:create-task:jab:TargetId={targetId}" },
		{ "ram", "command:create-task:ram:TargetId={targetId}" },
		{ "attack-pos", "command:create-task:attack:Name=static attack:{wPos}" },
		{ "su launch", "su:command:create-task:ram:{wPos}" },
		{ "attack", "command:create-task:attack:TargetId={targetId}" },
		{ "follow-enemy?", "" },
		{ "remove", "command:remove-task:{targetId}" }, // what if I select many units and remove task?
		{ "dock", "command:create-task:dock:TargetId={targetId}" },
		{ "follow", "command:create-task:follow:TargetId={targetId}" },
		{ "move", "command:create-task:move:{wPos}" },
		{ "wait-for-signal", "command:create-task:wait-for-signal" },
		{ "wait-10-s", "command:create-task:wait:Ticks=600" },
		{ "repeat", "command:create-task:exec:Name=Repeat,FollowUp=command;repeat" },
		{ "land", "command:create-task:land:Name=NG-aligned landing:{wPos}" },
		{ "response:free-fire", "command:exec:FollowUp=command;set-response;FreeFire" },
		{ "response:attack", "command:exec:FollowUp=command;set-response;Attack" },
		// add here new APck-compatible commands
		{ "next", "command:next" }
	};

// menu (hold D) for non-hover (no context)
List<string> commandsForNonHover = new List<string>() { "move", "wait-for-signal", "wait-10-s", "repeat", "response:free-fire", "response:attack", "next" };

// context menu (hold D) for static raycasted point hover (diamond mark)
List<string> unitToStaticTargetCommands = new List<string>() { "jab", "ram", "attack-pos", "su launch", "land" };

// context menu (hold D) for datalink target hover (red mark)
List<string> unitToDatalinkTargetCommands = new List<string>() { "jab", "ram", "attack" };

// context menu (hold D) for task node hover
List<string> unitToTaskCommands = new List<string>() { "remove" };

// context menu (hold D) for hovering over another unit while having units selected
List<string> unitToUnitCommands = new List<string>() { "dock", "follow" };

string BlackScreensTag = "[CommanderViewScreen]";

static class Variables
{
	static Dictionary<string, object> v = new Dictionary<string, object> {
			{ "bool", new Variable<bool> { value = false, parser = s => s == "true" } },
			{ "centerOffsetY", new Variable<float> { value = 0f, parser = s => float.Parse(s) } },
			{ "interface-upscale", new Variable<float> { value = 1f, parser = s => float.Parse(s) } },
			{ "int", new Variable<int> { value = 5, parser = s => int.Parse(s) } }
	};
	public static void Set(string key, string value) { (v[key] as ISettable).Set(value); }
	public static void Set<T>(string key, T value) { (v[key] as ISettable).Set(value); }
	public static T Get<T>(string key) { return (v[key] as ISettable).Get<T>(); }
	public interface ISettable
	{
		void Set(string v);
		T1 Get<T1>();
		void Set<T1>(T1 v);
	}
	public class Variable<T> : ISettable
	{
		public T value;
		public Func<string, T> parser;
		public void Set(string v) { value = parser(v); }
		public void Set<T1>(T1 v) { value = (T)(object)v; }
		public T1 Get<T1>() { return (T1)(object)value; }
	}
}
class Ȏ{static Ȏ ȏ;Ȏ(){}Action<string>Ȑ;Dictionary<string,bool>ȑ;Ȏ(Dictionary<string,bool>Ȓ,Action<string>ȓ){Ȑ=ȓ;ȑ=Ȓ;}
public static Ȏ Ȕ=>ȏ;public static void Ž(Dictionary<string,bool>Ȓ,Action<string>ȓ){if(ȏ==null)ȏ=new Ȏ(Ȓ,ȓ);}public void ȕ(
string ȍ,bool Ȗ){if(ȑ[ȍ]!=Ȗ)ȗ(ȍ);}public void ȗ(string ȍ){ȑ[ȍ]=!ȑ[ȍ];Ȑ(ȍ);}public bool Ȧ(string ȍ){return ȑ[ȍ];}}bool ȧ;ȩ Ȩ;
class ȩ{Dictionary<string,Action<string[]>>ê;public ȩ(Dictionary<string,Action<string[]>>ê){this.ê=ê;}public void Ȫ(string ǡ,
string[]ȫ){this.ê[ǡ].Invoke(ȫ);}}int Ȭ;void ȭ(string Ȯ){Ȭ++;ŷ.Ż++;ŷ.ƀ("Run count: "+Ȭ);ŷ.ƀ("IGC.Me: "+IGC.Me);if(ȧ&&string.
IsNullOrEmpty(Ȯ)){ȧ=false;Ȯ=string.Join(",",Me.CustomData.Trim('\n').Split(new[]{'\n'},StringSplitOptions.RemoveEmptyEntries).Where(Ɓ
=>!Ɓ.StartsWith("//")).Select(Ɓ=>"["+Ɓ+"]"));}if(!string.IsNullOrEmpty(Ȯ)&&Ȯ.Contains(":")){var ê=Ȯ.Split(new[]{"],["},
StringSplitOptions.RemoveEmptyEntries).Select(Ɓ=>Ɓ.Trim('[',']')).ToList();foreach(var ę in ê){string[]ȫ=ę.Split(new[]{':'},
StringSplitOptions.RemoveEmptyEntries);if(ȫ[0]=="command"){try{this.Ȩ.Ȫ(ȫ[1],ȫ);}catch(Exception ex){ŷ.ƀ("Unknown command, cmdParts: "+ę);
throw ex;}}if(ȫ[0]=="toggle"){Ȏ.Ȕ.ȗ(ȫ[1]);ŷ.Ĝ(string.Format("Switching '{0}' to state '{1}'",ȫ[1],Ȏ.Ȕ.Ȧ(ȫ[1])));}}}}void ȯ(){
}List<IMyCameraBlock>Ȱ=new List<IMyCameraBlock>();List<IMySensorBlock>ȱ=new List<IMySensorBlock>();IMyRemoteControl Ȳ;
public enum ȳ{ȵ=0,ȴ=1,ȥ=2,Ȥ=3}ȳ Ș;int ș=1;void Ț(){if(!string.IsNullOrEmpty(Me.CustomData))ȧ=true;ŷ.Ž(Ɓ=>Echo(Ɓ),
GridTerminalSystem,Me);List<IMyBlockGroup>ț=new List<IMyBlockGroup>();GridTerminalSystem.GetBlockGroups(ț);var Ȝ=ț.Where(ż=>{List<
IMyProgrammableBlock>ȝ=new List<IMyProgrammableBlock>();ż.GetBlocksOfType(ȝ);return ȝ.Contains(Me);}).FirstOrDefault();if(Ȝ==null){Runtime.
UpdateFrequency=UpdateFrequency.None;ŷ.ƀ("Can't find hardware group containing this PB, stopping now.");}else{List<IMyShipController>ŝ=
new List<IMyShipController>();Ȝ.GetBlocksOfType(ŝ);ƛ=new ƍ(ŝ,()=>Ȭ);List<IMyTextPanel>Ȟ=new List<IMyTextPanel>();Ȝ.
GetBlocksOfType(Ȟ);Ȟ.ForEach(Ɓ=>{Ɓ.ContentType=ContentType.SCRIPT;Ɓ.Script="";Ɓ.ScriptBackgroundColor=Color.Transparent;});var ȟ=Ȟ.
Where(ĝ=>!ĝ.CustomName.Contains(BlackScreensTag));if(ȟ.Any()){var đ=new ø(ȟ.First(),.5f,ɦ,ƛ,customIcons,commandsForNonHover);
X.Add(new ɛ(Me.CubeGrid,ɴ,ȟ.ToList(),ƛ,ɦ,đ));}var Ƞ=new List<IMyRemoteControl>();Ȝ.GetBlocksOfType(Ƞ);Ȳ=Ƞ.FirstOrDefault(
);var ȡ=Ȟ.FirstOrDefault(ĝ=>ĝ.CustomName.Contains(BlackScreensTag));var Ȣ=ŝ.FirstOrDefault(ĝ=>ĝ is IMyTextSurfaceProvider
);if(Ȣ!=null){var H=ȡ??((IMyTextSurfaceProvider)Ȣ).GetSurface(0);var ȣ=new ĸ(Ȣ);var đ=new ø(H,.5f,ɦ,ƛ,customIcons,
commandsForNonHover);X.Add(new Ċ(ƛ,ȣ,ɴ,H,ɦ,đ));}Ȝ.GetBlocksOfType(ȱ);ȱ.ForEach(Ɓ=>{Ɓ.DetectPlayers=true;Ɓ.DetectOwner=true;});Ȝ.
GetBlocksOfType(Ȱ);Ȱ.ForEach(Ɓ=>{Ɓ.EnableRaycast=true;});}Ȏ.Ž(new Dictionary<string,bool>{{"control",false},{"render-inactive-view",
true}},ȍ=>{switch(ȍ){case"control":if(Ȏ.Ȕ.Ȧ("control")){ƛ.Ş().SetValueBool("ControlGyros",false);IGC.SendBroadcastMessage(
"apck.command","toggle:ignore-user-thruster:true",TransmissionDistance.CurrentConstruct);}else{ƛ.Ş().SetValueBool("ControlGyros",true)
;IGC.SendBroadcastMessage("apck.command","toggle:ignore-user-thruster:false",TransmissionDistance.CurrentConstruct);}
break;case"render-inactive-view":X.ForEach(Z=>Z.Ʌ());break;}});this.Ȩ=new ȩ(new Dictionary<string,Action<string[]>>{{
"set-value",(ǟ)=>Variables.Set(ǟ[2],ǟ[3])},{"cycle-view",(ǟ)=>Y()},{"commit",(ǟ)=>{foreach(var ǡ in ɴ.ɽ){if(IGC.IsEndpointReachable
(ǡ))IGC.SendUnicastMessage(ǡ,"apck.command",$"command:signal");}}},{"cast",(ǟ)=>{var Z=X.ElementAt(W);Z.ʇ(Ȱ,Ğ=>{var Ǣ=ǚ(-
1,Ğ.EntityId,Ğ.Name,Ğ.HitPosition.Value,Vector3D.Zero);ɴ.ɹ.Add(Ǣ);});}}});}static void ǣ<Ż>(Ż Ǥ,IList<Ż>ę)where Ż:class{
if((Ǥ!=null)&&!ę.Contains(Ǥ))ę.Add(Ǥ);}Ż ǥ<Ż>(Func<IMyTerminalBlock,bool>Ǧ)where Ż:class{var ǧ=new List<IMyTerminalBlock>(
);GridTerminalSystem.GetBlocksOfType(ǧ,ų=>((ų is Ż)&&Ǧ(ų)));return ǧ.First()as Ż;}Program(){Runtime.UpdateFrequency=
UpdateFrequency.Update1;Ț();}class Ǩ{public bool ǩ;public ǌ Ǫ;public List<string>ǫ{get;private set;}public string Ǭ;public Ǩ(ǌ ǭ,List<
string>Ǯ,string ǯ,bool ǰ){Ǫ=ǭ;ǫ=Ǯ;Ǭ=ǯ;ǩ=ǰ;}public long ǲ;public long Ǳ;public Func<ǌ,ǌ>Ǡ;public Func<ǌ,ǌ>Ǟ;public Action<ø,
RectangleF,Vector2>ǉ;public Action<Vector3D,string,Ǩ>Ǌ;public Action ǋ;}struct ǌ{public Vector3D Ǎ;public Vector3D ǎ;public string
Ǐ;public List<MySprite>ǐ;public Vector2 Ǒ;public Color Ɨ;public float ǒ;public string Ǔ;public float ǔ;public Ǩ Ǖ;}
IEnumerable<MyTuple<Ż,long>>ǖ<Ż>(IMyIntergridCommunicationSystem Ǘ,string ǘ){var ź=Ǘ.RegisterBroadcastListener(ǘ);while(ź.
HasPendingMessage){var Ǚ=ź.AcceptMessage();yield return new MyTuple<Ż,long>((Ż)Ǚ.Data,Ǚ.Source);}}ǌ ǚ(long Ƭ,long Ǜ,string ǜ,Vector3D ǝ,
Vector3D ǈ){var ǳ=new ǌ();ǳ.Ǎ=ǝ;ǳ.Ɨ=Color.Aqua;ǳ.Ǐ=@"SquareHollow";ǳ.Ǒ=new Vector2(15,15);ǳ.ǒ=(float)Math.PI/4f;ǳ.Ǔ="static:"+ǜ;
ǳ.ǔ=1.5f;ǳ.Ǖ=new Ǩ(ǳ,unitToStaticTargetCommands,"attack-pos",false);ǳ.Ǖ.Ǳ=Ǜ;ǳ.Ǖ.Ǡ=ĝ=>{ĝ.ǒ=Ȭ/100f;ĝ.Ǔ="go";return ĝ;};ǳ.Ǖ.
ǋ=()=>{foreach(var Ǻ in ɴ.ɱ()){if(unitToStaticTargetCommands.Contains(ɦ.ɨ))Ǻ.Ǖ.Ǌ?.Invoke(ǝ,ɦ.ɨ,ǳ.Ǖ);else Ǻ.Ǖ.Ǌ?.Invoke(ǝ,
ǳ.Ǖ.Ǭ,ǳ.Ǖ);}};return ǳ;}void ȃ(){foreach(var ǿ in ǖ<MyTuple<MyTuple<string,long,long,byte,byte>,Vector3D,Vector3D,MatrixD
,BoundingBoxD>>(IGC,"tgp.global.gridsense.update")){var Ƭ=ǿ.Item2;var ȁ=ǿ.Item1;var Ǵ=ȁ.Item1;var ǝ=ȁ.Item2;var ǈ=ȁ.Item3
;var µ=ȁ.Item4;var Ȅ=ȁ.Item5;var ǳ=new ǌ();ǳ.Ǎ=ǝ;ǳ.Ɨ=Color.Red;ǳ.Ǐ=@"SquareHollow";ǳ.Ǒ=new Vector2(15,15);ǳ.ǒ=(float)Math
.PI/4f;ǳ.Ǔ="tgp.global:"+Ǵ.Item1;ǳ.ǔ=1.5f;var Ǜ=Ǵ.Item2;ǳ.Ǖ=new Ǩ(ǳ,unitToDatalinkTargetCommands,"attack",false);ǳ.Ǖ.Ǳ=Ǜ;
ǳ.Ǖ.Ǡ=ĝ=>{ĝ.ǒ=Ȭ/100f;ĝ.Ǔ="attack";return ĝ;};ǳ.Ǖ.ǋ=()=>{foreach(var Ǻ in ɴ.ɱ()){if(unitToDatalinkTargetCommands.Contains(
ɦ.ɨ))Ǻ.Ǖ.Ǌ?.Invoke(ǝ,ɦ.ɨ,ǳ.Ǖ);else Ǻ.Ǖ.Ǌ?.Invoke(ǝ,ǳ.Ǖ.Ǭ,ǳ.Ǖ);}};ɴ.ɶ.Add(ǳ);}}void ȅ(){foreach(var ǿ in ǖ<ImmutableArray<
MyTuple<int,string,Vector3D>>>(IGC,"captain-commander.task-data")){var o=ƛ.Ş();var Ȇ=ǿ.Item1.Length;if(Ȇ>1){int ȇ=0;for(int Ĉ=1
;Ĉ<Ȇ;Ĉ++){var Ȉ=ǿ.Item1[Ĉ];var ȉ=Ȉ.Item3;if(ǿ.Item1[Ĉ-1].Item3!=ȉ){ȇ=0;ɴ.ɺ.Add(new MyTuple<Vector3D,Vector3D,Vector4>(ǿ.
Item1[Ĉ-1].Item3,ȉ,Color.PaleGreen.ToVector4()));}else{if(o!=null){double B;var Ȋ=X.ElementAt(W)as Ċ;if(Ȋ!=null)B=(Ȋ.ē()-ȉ).
Length();else B=(o.WorldMatrix.Translation-ȉ).Length();ȇ++;ȉ=ȉ+o.WorldMatrix.Down*ȇ*(B/50f);}}var ǳ=new ǌ();var ȋ=Ȉ.Item2;if(
customIcons.ContainsKey(ȋ))ǳ.ǐ=customIcons[ȋ].Ǉ(Vector2.Zero,new Vector2(15,15));ǳ.Ǐ=
@"Textures\FactionLogo\Builders\BuilderIcon_2.dds";ǳ.Ǎ=ȉ;ǳ.Ɨ=Color.Green;ǳ.Ǒ=new Vector2(15,15);ǳ.Ǔ=Ȉ.Item2;ǳ.ǔ=1.5f;ǳ.Ǖ=new Ǩ(ǳ,unitToTaskCommands,"remove",false);ǳ.Ǖ.Ǳ=
Ȉ.Item1;ǳ.Ǖ.Ǡ=ĝ=>{ĝ.ǐ=null;ĝ.Ɨ=Color.Goldenrod;ĝ.Ǒ=new Vector2(30,30);ĝ.ǒ=Ȭ/100f;ĝ.Ǔ="delete";return ĝ;};ǳ.Ǖ.ǋ=()=>{ŷ.Ĝ(
$"Remove task {Ȉ.Item1} command for {ǿ.Item2}");IGC.SendUnicastMessage(ǿ.Item2,"apck.command",$"command:remove-task:{Ȉ.Item1}");};ɴ.ɶ.Add(ǳ);}}}}void Ȍ(){foreach(var
ǿ in ǖ<MyTuple<MyTuple<string,byte,int,long>,Vector3D,Vector3D,Vector3D,string>>(IGC,"apck.report")){var ȁ=ǿ.Item1;var Ǵ=
ȁ.Item1;var ǵ=Ǵ.Item1;var Ƕ=Ǵ.Item2;var Ƿ=Ǵ.Item3;var ǝ=ȁ.Item2;var ǈ=ȁ.Item3;var Ǹ=ȁ.Item4;var ǹ=ȁ.Item5;var ǳ=new ǌ();ǳ
.Ǎ=ǝ;ǳ.Ɨ=Color.Green;if(!string.IsNullOrEmpty(ǵ))ǳ.Ǔ=ǵ;ǳ.ǔ=0.7f;ǳ.Ǐ="Triangle";ǳ.Ǒ=new Vector2(15,15);ǳ.ǒ=3.14f;var Ǜ=ǿ.
Item2;ǳ.Ǖ=new Ǩ(ǳ,unitToUnitCommands,"follow",true);ǳ.Ǖ.Ǳ=Ǜ;ǳ.Ǖ.Ǡ=ĝ=>{ĝ.Ǐ="SquareHollow";ĝ.Ɨ=Color.Goldenrod;ĝ.Ǒ=new Vector2(
30,30);ĝ.ǒ=Ȭ/100f;ĝ.Ǔ=ǹ;return ĝ;};ǳ.Ǖ.Ǟ=ĝ=>{ĝ.Ɨ=Color.DarkSeaGreen;ĝ.Ɨ.A=100;ĝ.Ǔ=ǹ;ĝ.ǔ=2f;return ĝ;};ǳ.Ǖ.ǲ=ǿ.Item2;ǳ.Ǖ.ǋ=
()=>{foreach(var Ǻ in ɴ.ɱ()){if(unitToUnitCommands.Contains(ɦ.ɨ))Ǻ.Ǖ.Ǌ?.Invoke(ǝ,ɦ.ɨ,ǳ.Ǖ);else Ǻ.Ǖ.Ǌ?.Invoke(ǝ,ǳ.Ǖ.Ǭ,ǳ.Ǖ)
;}};ǳ.Ǖ.Ǌ=(I,u,ç)=>{if(ɴ.ɲ!=null)I=I-(ɴ.ɲ.Value-ǳ.Ǎ);ŷ.Ĝ(
$"Got a click while selected {Ǜ}. Hovered obj: {ç?.Ǳ}. Cmd tag: {u}");u=tasksMapping[u];if(ç!=null)u=u.Replace("{targetId}",ç.Ǳ.ToString()).Replace("{wPos}",ŵ(ç.Ǫ.Ǎ));else u=u.Replace(
"{wPos}",ŵ(I));ŷ.Ĝ($"Sending cmd '{u}'");IGC.SendUnicastMessage(ǳ.Ǖ.ǲ,"apck.command",u);};ɴ.ɶ.Add(ǳ);var ǻ=ǈ.Length();if(ǻ>1){
var Ǽ=new ǌ();Ǽ.Ǎ=ǝ+ǈ;Ǽ.Ɨ=Color.Green;Ǽ.ǔ=0.7f;Ǽ.Ǐ="Circle";Ǽ.Ǒ=new Vector2(5,5);Ǽ.Ǔ=$"\nP{ǻ:f1}";ɴ.ɶ.Add(Ǽ);var ę=Color.
SeaGreen;ę.A=40;ɴ.ɺ.Add(new MyTuple<Vector3D,Vector3D,Vector4>(ǝ,ǝ+ǈ,ę.ToVector4()));if(Ǹ!=Vector3D.Zero){var ǽ=new ǌ();ǽ.Ǎ=Ǹ;ǽ.
Ɨ=Color.Green;ǽ.ǔ=0.7f;ǽ.Ǐ="CircleHollow";ǽ.Ǒ=new Vector2(10,10)*(2+(float)Math.Sin(Ȭ/30f)*0.5f);ɴ.ɶ.Add(ǽ);}}}}void Ǿ(
MyIGCMessage ǿ){if(ǿ.Tag.StartsWith("hud")){if(ǿ.Tag.Contains("proj")){var Ȁ=(ImmutableArray<MyTuple<string,Vector3D,ImmutableArray<
string>>>)ǿ.Data;foreach(var ȁ in Ȁ){var Ȃ=ȁ.Item1;var ǝ=ȁ.Item2;var ǹ=ȁ.Item3;if(ǿ.Tag.Contains("apck")){var ǳ=new ǌ();ǳ.Ǎ=ǝ;
ǳ.Ɨ=Color.Red;ǳ.ǔ=0.7f;if(Ȃ.Contains("agent-pos")){ǳ.Ǐ="Triangle";ǳ.Ǒ=new Vector2(15,15);ǳ.ǒ=3.14f;}else if(Ȃ.Contains(
"agent-vel")){ǳ.Ǐ="Circle";ǳ.Ǒ=new Vector2(5,5);ǳ.Ǔ=ǹ[0];}else if(Ȃ.Contains("agent-dest")){ǳ.Ǐ="CircleHollow";ǳ.Ǒ=new Vector2(10,
10)*(2+(float)Math.Sin(Ȭ/30f)*0.5f);}else if(Ȃ.Contains("agent-aim")){ǳ.Ǐ="Cross";ǳ.Ǒ=new Vector2(10,10);}else if(Ȃ.
Contains("su-pos")){ǳ.Ǐ="Circle";ǳ.Ǒ=new Vector2(3,3);ǳ.Ɨ=Color.Red;}ɴ.ɶ.Add(ǳ);}if(ǿ.Tag.Contains("tgp")){var ǳ=new ǌ();ǳ.Ǎ=ǝ;ǳ
.Ɨ=Color.White;ǳ.ǔ=0.7f;if(Ȃ.Contains("local-target-current-pos")){ǳ.Ǐ="CircleHollow";ǳ.Ǒ=new Vector2(15f,15f);ǳ.Ǖ=new Ǩ(
ǳ,unitToDatalinkTargetCommands,"attack",false);ǳ.Ǖ.ǲ=ǿ.Source;ǳ.Ǖ.Ǡ=ĝ=>{ĝ.Ǐ="CircleHollow";ĝ.Ɨ=Color.Goldenrod;ĝ.Ǒ=new
Vector2(30,30);return ĝ;};ǳ.Ǖ.ǋ=()=>IGC.SendUnicastMessage(ǳ.Ǖ.ǲ,"apck.command","toggle:broadcast-vectors");}else if(Ȃ.Contains
("datalink-target")){ǳ.Ǐ="Screen_LoadingBar";ǳ.Ǒ=new Vector2(25f,25f);ǳ.ǒ=Ȭ/100f;}else if(Ȃ.Contains("lom-offset")){ǳ.Ǐ=
"CircleHollow";ǳ.Ǒ=new Vector2(10f,10f);}else if(Ȃ.Contains("info-vel-dist")){ǳ.Ǐ="CircleHollow";ǳ.Ǒ=new Vector2(10f,10f);ǳ.Ǔ=ǹ[0];}
else if(Ȃ.Contains("scanlock")){ǳ.Ǐ="SquareHollow";ǳ.Ǒ=new Vector2(40f,40f);ǳ.ǒ=Ȭ/100f;}ɴ.ɶ.Add(ǳ);}}}}else{if(ǿ.Tag.
Contains("draw-text")){var ȁ=(MyTuple<string,Vector2,float>)ǿ.Data;var Ʉ=MySprite.CreateText(ȁ.Item1,"Debug",new Color(1f),ȁ.
Item3*Variables.Get<float>("interface-upscale"),TextAlignment.LEFT);Ʉ.Position=ȁ.Item2;ɴ.ɷ.Add(Ʉ);}if(ǿ.Tag.Contains(
"persist-text")){var ȁ=(MyTuple<string,Vector2,float>)ǿ.Data;var Ʉ=MySprite.CreateText(ȁ.Item1,"Debug",Color.DarkRed,ȁ.Item3*Variables
.Get<float>("interface-upscale"),TextAlignment.LEFT);Ʉ.Position=ȁ.Item2;ɴ.ɸ.Add(Ʉ);}if(ǿ.Tag.Contains("draw-sprite")){var
ȁ=(MyTuple<string,Vector2,Vector2,float>)ǿ.Data;var Ʉ=new MySprite(SpriteType.TEXTURE,ȁ.Item1,size:ȁ.Item2*Variables.Get<
float>("interface-upscale"),color:new Color(1f));Ʉ.Position=ȁ.Item3;Ʉ.RotationOrScale=ȁ.Item4;ɴ.ɷ.Add(Ʉ);}if(ǿ.Tag.Contains(
"draw-lines")){var ȁ=(ImmutableArray<MyTuple<Vector3D,Vector3D,Vector4>>)ǿ.Data;ɴ.ɺ.AddRange(ȁ);}if(ǿ.Tag.Contains("persist-line")){
var ȁ=(MyTuple<Vector3D,Vector3D,Vector4>)ǿ.Data;ɴ.ɻ.Add(ȁ);}if(ǿ.Tag.Contains("draw-projection")){var ȁ=(MyTuple<string,
Vector2,Vector3D,Vector3D,float,string>)ǿ.Data;var ǳ=new ǌ();ǳ.Ǎ=ȁ.Item3;ǳ.Ɨ=new Color(1f);ǳ.ǎ=ȁ.Item4;ǳ.Ǐ=ȁ.Item1;ǳ.Ǒ=ȁ.Item2;
ǳ.ǒ=ȁ.Item5;ǳ.Ǔ=ȁ.Item6;ɴ.ɶ.Add(ǳ);}if(ǿ.Tag.Contains("persist-projection")){var ȁ=(MyTuple<string,Vector2,Vector3D,
Vector3D,float,string>)ǿ.Data;var ǳ=new ǌ();ǳ.Ǎ=ȁ.Item3;ǳ.Ɨ=new Color(1f);ǳ.ǎ=ȁ.Item4;ǳ.Ǐ=ȁ.Item1;ǳ.Ǒ=ȁ.Item2;ǳ.ǒ=ȁ.Item5;ǳ.Ǔ=ȁ.
Item6;ɴ.ɹ.Add(ǳ);}if(ǿ.Tag.Contains("persist-arr")){var ɳ=(ImmutableArray<MyTuple<string,Vector2,Vector3D,Vector3D,float,
string>>)ǿ.Data;foreach(var ȁ in ɳ){var ǳ=new ǌ();ǳ.Ǎ=ȁ.Item3;ǳ.Ɨ=new Color(1f);ǳ.ǎ=ȁ.Item4;ǳ.Ǐ=ȁ.Item1;ǳ.Ǒ=ȁ.Item2;ǳ.ǒ=ȁ.
Item5;ǳ.Ǔ=ȁ.Item6;ɴ.ɹ.Add(ǳ);}}}}ɵ ɴ=new ɵ();class ɵ{public List<ǌ>ɶ=new List<ǌ>();public List<MySprite>ɷ=new List<MySprite>(
);public List<MySprite>ɸ=new List<MySprite>();public List<ǌ>ɹ=new List<ǌ>();public List<MyTuple<Vector3D,Vector3D,Vector4
>>ɺ=new List<MyTuple<Vector3D,Vector3D,Vector4>>();public List<MyTuple<Vector3D,Vector3D,Vector4>>ɻ=new List<MyTuple<
Vector3D,Vector3D,Vector4>>();public HashSet<Ǩ>ɼ=new HashSet<Ǩ>();public Ǩ ɾ;public List<long>ɽ=new List<long>();public Vector3D
?ɲ;public IEnumerable<ǌ>ɱ(){return ɶ.Where(ĝ=>(ĝ.Ǖ?.Ǌ!=null)&&ɽ.Contains(ĝ.Ǖ.ǲ));}public void ɤ(){var ɥ=ɱ();if(ɥ.Any()){
var Ń=new Vector3D();foreach(var B in ɥ){Ń+=B.Ǎ;}Ń/=ɥ.Count();ɲ=Ń;}else ɲ=null;}}ɧ ɦ=new ɧ();class ɧ{public string ɨ="move"
;public List<ɪ>ɩ=new List<ɪ>();}class ɪ{public long Ǳ;public List<ǌ>ɫ=new List<ǌ>();}void Main(string ɬ,UpdateType ɭ){try
{ŷ.ƀ($"APck/Command v.{Ver}");ƛ.ť();var ɮ=IGC.RegisterBroadcastListener("something.command");if(ɮ.HasPendingMessage){var
ǿ=ɮ.AcceptMessage();ɬ=ǿ.Data.ToString();}ȃ();ȅ();Ȍ();while(IGC.UnicastListener.HasPendingMessage){var ǿ=IGC.
UnicastListener.AcceptMessage();if(ǿ.Tag.Contains("command")){ɬ=ǿ.Data.ToString();break;}try{Ǿ(ǿ);}catch(Exception ex){ŷ.ƀ(
$"Handle msg fail. Tag: {ǿ.Tag}");ŷ.ƀ($"Type: {ǿ.Data.GetType()}");throw ex;}}ɴ.ɷ.AddRange(ɴ.ɸ);ɴ.ɶ.AddRange(ɴ.ɹ);ɴ.ɺ.AddRange(ɴ.ɻ);ɴ.ɤ();if((ɴ.ɺ.Count>
0)||(ɴ.ɷ.Count>0)||(ɴ.ɶ.Count>0)){RayD?j=null;Vector3D?k=null;var o=ƛ.Ş();if(X.Any(ĝ=>ĝ is ɛ))f(ref j,ref k,o);foreach(
var G in X){var ɯ=X.ElementAt(W)==G;if(ɯ||Ȏ.Ȕ.Ȧ("render-inactive-view")){G.Ɇ(o,Ȏ.Ȕ.Ȧ("control")&&(o!=null)&&ɯ);G.Ƣ(j,k);if(
ɯ)G.ȶ();}}ŷ.ƀ($"spritesToDraw: {ɴ.ɷ.Count}");ŷ.ƀ($"projectionsToDraw: {ɴ.ɶ.Count}");ŷ.ƀ($"persSprites: {ɴ.ɸ.Count}");ŷ.ƀ(
$"depthObjects: {ɴ.ɶ.Count}");ŷ.ƀ($"SelectedIds: {ɴ.ɽ.Count}");ɴ.ɼ.Clear();ɴ.ɾ=null;ɴ.ɷ.Clear();ɴ.ɺ.Clear();ɴ.ɶ.Clear();}ȭ(ɬ);ȯ();ŷ.ƀ(
"CurrentPovType: "+Ș);ŷ.ƀ("Processed in "+Runtime.LastRunTimeMs.ToString("f3")+" ms");}catch(Exception ex){ŷ.Ĝ(
"Critical error, stopping now");ŷ.Ĝ(ex.ToString());Runtime.UpdateFrequency=UpdateFrequency.None;}}abstract class ɰ{protected Vector2 ɣ;protected
Vector2 ɿ;protected Vector3 ʀ;protected ƍ û;protected ɵ ʍ;protected float ʎ=1f;protected float ʏ;protected bool ʐ;protected
Vector3D?ʑ;protected ɧ ɦ;protected ø ʒ;protected ɰ(ɧ ʓ,ƍ ă,ɵ Đ,float ʔ,ø đ){ɦ=ʓ;û=ă;ʍ=Đ;ʎ=ʔ;ʒ=đ;ʒ.á=(u,ʕ)=>ȸ(u,ʕ);}public
abstract bool N(Vector3D O,IMyTextSurface Q,Vector3D k,bool Ģ,out Vector2 R);public abstract RayD ʖ(IMyShipController ę,Vector2
Ġ);protected bool ʗ(RayD ń,Vector2 ʘ,Vector3D Ń,IMyShipController ę){var M=ʖ(ę,ʘ);var ʁ=Vector3D.Zero;double?B;Vector3D ʂ
=Vector3D.Zero;if(ę?.TryGetPlanetPosition(out ʂ)==true){var ʃ=(Ń-ʂ).Length();var H=new BoundingSphereD(ʂ,ʃ);B=M.
Intersects(H);if(B.HasValue){var I=ń.Position+B.Value*M.Direction;ʁ=Vector3D.Normalize(I-ʂ);}}else{var ʄ=new PlaneD(Ń,ń.Direction)
;B=M.Intersects(ʄ);if(B.HasValue){ʁ=ń.Direction;}}if(B.HasValue){ʑ=ń.Position+B.Value*M.Direction+ʁ*ʏ;return true;}ʑ=null
;return false;}protected Action<MyDetectedEntityInfo>ʅ;protected List<IMyCameraBlock>ʆ;public void ʇ(List<IMyCameraBlock>
A,Action<MyDetectedEntityInfo>ʈ){ʆ=A;ʅ=ʈ;}protected bool ʉ(ref MyTuple<Vector3D,Vector3D>ĺ,PlaneD J){var U=J;var ʊ=U.
DistanceToPoint(ĺ.Item1);var ʋ=U.DistanceToPoint(ĺ.Item2);if((ʊ>0)&&(ʋ>0))return true;if((ʊ<=0)&&(ʋ<=0)){return false;}if(ʊ>0){var M=
Vector3D.Normalize(ĺ.Item2-ĺ.Item1);var İ=new RayD(ĺ.Item1,M);var ī=İ.Intersects(U);if(!ī.HasValue)return false;ĺ.Item2=İ.
Position+İ.Direction*(ī.Value-100);return true;}else{var M=Vector3D.Normalize(ĺ.Item1-ĺ.Item2);var İ=new RayD(ĺ.Item2,M);var ī=İ
.Intersects(U);if(!ī.HasValue)return false;ĺ.Item1=İ.Position+İ.Direction*(ī.Value-100);return true;}}public MySprite ʌ(
MyTuple<Vector3D,Vector3D>ĺ,float Ɂ,Color Ƚ,IMyTextSurface Q,Vector3D k,PlaneD J){if(ʉ(ref ĺ,J)){Vector2 Ⱦ,ȿ;if(N(ĺ.Item1,Q,k,
true,out Ⱦ)&&N(ĺ.Item2,Q,k,true,out ȿ)){return ɀ(Ⱦ,ȿ,Ɂ,Ƚ);}}return new MySprite();}public MySprite ɀ(Vector2 Ƃ,Vector2 ų,
float Ɂ,Color Ƚ){var ɂ=ų-Ƃ;var Ƀ=new Vector2(0,1);float ř=(float)(Math.Atan2(Ƀ.Y,Ƀ.X)-Math.Atan2(ɂ.Y,ɂ.X));var Ʉ=new MySprite
(SpriteType.TEXTURE,"SquareSimple",size:new Vector2(Ɂ,ɂ.Length()),color:Ƚ);Ʉ.Position=Ƃ+ɂ/2;Ʉ.RotationOrScale=-ř;return Ʉ
;}public abstract void Ƣ(RayD?j,Vector3D?k);public abstract void Ʌ();public abstract void Ɇ(IMyShipController Ł,bool ł);
protected bool ɇ(){ʐ=false;var L=û.ũ();ɿ.X=L.Y;ɿ.Y=L.X;ɣ+=ɿ;ʀ=û.Ŧ();return(L.LengthSquared()>0||ʀ.LengthSquared()>0);}protected
Vector2?Ɉ;protected Vector2?ɉ;bool ɋ;protected void Ɋ(string ȍ){bool ȼ=û.ő(ȍ);if(û.Ŏ(ȍ)){if(û.ũ().LengthSquared()>0){if(!Ɉ.
HasValue)Ɉ=ɣ;ɉ=ɣ;}}else{if(ȼ){ɋ=true;}}}public void ȶ(){if(!ʐ)ȷ();if(ɋ){ɋ=false;if(Ɉ.HasValue){ʍ.ɽ=ʍ.ɼ.Where(ĝ=>ĝ.ǩ).Select(ĝ=>ĝ
.ǲ).ToList();ŷ.Ĝ($"Rect select, new count: {ʍ.ɽ.Count}");foreach(var ǡ in ʍ.ɽ)ŷ.Ĝ($"Selected: {ǡ}");}Ɉ=null;ɉ=null;}}
protected void ȷ(){bool Ľ=false;if(ʍ.ɾ!=null){if(û.ő("e")&&(ɉ==null)){ŷ.Ĝ($"{this.GetType().Name}: hover click!");ʍ.ɾ.ǋ?.Invoke()
;Ľ=true;}}if(!Ľ){if(ʑ.HasValue&&û.ő("e")&&(ɉ==null)){ŷ.Ĝ($"{this.GetType().Name}: free click!");ȸ(ɦ.ɨ,ʍ.ɾ);}}}public void
ȸ(string u,Ǩ ç){foreach(var é in ʍ.ɱ()){é.Ǖ.Ǌ(ʑ.Value,u,ç);}}public IEnumerable<MySprite>ȹ(ǌ I,Vector3D?k,IMyTextSurface
Q,ñ Ⱥ){Vector2 Ȼ;if(N(I.Ǎ,Q,k??I.ǎ,false,out Ȼ)){foreach(var Ɓ in ȹ(I,Ȼ,Ⱥ))yield return Ɓ;}}public IEnumerable<MySprite>ȹ
(ǌ I,Vector2 Ȼ,ñ Ⱥ){if(Ⱥ.ö){if(I.Ǖ!=null){var Ė=new Vector2(Math.Min(Ⱥ.ó.X,Ⱥ.ô.X),Math.Min(Ⱥ.ó.Y,Ⱥ.ô.Y));var D=new
Vector2(Math.Max(Ⱥ.ó.X,Ⱥ.ô.X),Math.Max(Ⱥ.ó.Y,Ⱥ.ô.Y));var Ȅ=new RectangleF(Ė,D-Ė);if(Ȅ.Contains(Ȼ)){ʍ.ɼ.Add(I.Ǖ);if(I.Ǖ.Ǡ!=null)
I=I.Ǖ.Ǡ(I);}}}else if(Ⱥ.õ){if(I.Ǖ!=null){var é=I.Ǖ;var Ȅ=new RectangleF(Ȼ-I.Ǒ/2f,I.Ǒ);if(Ȅ.Contains(Ⱥ.ò)){ʍ.ɾ=é;if(é.Ǡ!=
null)I=é.Ǡ(I);if(û.Ŏ("d"))é.ǉ?.Invoke(ʒ,Ȅ,Ⱥ.ò);}}}if((I.Ǖ!=null)&&ʍ.ɽ.Contains(I.Ǖ.ǲ)&&(I.Ǖ.Ǟ!=null)){I=I.Ǖ.Ǟ(I);}if(I.ǐ!=
null){foreach(var Ɍ in I.ǐ){var ɍ=Ɍ;ɍ.Position=Ɍ.Position+Ȼ;yield return ɍ;}}else{var ɗ=new MySprite(SpriteType.TEXTURE,I.Ǐ,
size:I.Ǒ*ʎ,color:I.Ɨ);ɗ.Position=Ȼ;ɗ.RotationOrScale=I.ǒ;yield return ɗ;}if(!string.IsNullOrEmpty(I.Ǔ)){var ǹ=MySprite.
CreateText(I.Ǔ,"Debug",I.Ɨ,0.5f*ʎ,TextAlignment.LEFT);ǹ.Position=Ȼ;yield return ǹ;}}StringBuilder ɘ=new StringBuilder();protected
void ə(string Ɓ){ɘ.AppendLine(Ɓ);}protected string ɚ(){var Ɓ=ɘ.ToString();ɘ.Clear();return Ɓ;}}class ɛ:ɰ{List<IMyTextPanel>ɜ
=new List<IMyTextPanel>();IMyCubeGrid ɝ;public ɛ(IMyCubeGrid ɞ,ɵ Đ,List<IMyTextPanel>Ȟ,ƍ ă,ɧ Ă,ø đ):base(Ă,ă,Đ,1,đ){ɝ=ɞ;ɜ
=Ȟ;}public override void Ʌ(){foreach(var Q in ɜ){using(var Ē=Q.DrawFrame()){Ē.Add(new MySprite());}Q.ContentType=
ContentType.TEXT_AND_IMAGE;Q.ContentType=ContentType.SCRIPT;}}public override void Ƣ(RayD?j,Vector3D?k){ə($"rE: {ɉ}");ə($"rS: {Ɉ}")
;foreach(var Q in ɜ){bool ɟ=false;using(var Ē=Q.DrawFrame()){bool Ħ=Q.CubeGrid.GridSizeEnum==MyCubeSize.Large;var ɠ=Q.
TextureSize.Y/(Q.CubeGrid.GridSize*0.87f);float ɢ;float ɡ=Variables.Get<float>("centerOffsetY");if(Ħ)ɢ=(ɡ==0?-0.5f:ɡ)*ɠ;else ɢ=ɡ*ɠ;
if(j.HasValue&&(j.Value.Intersects(Q.WorldVolume).HasValue)){foreach(var Ɓ in ʍ.ɷ){MySprite ɗ=Ɓ;ɗ.Position=new Vector2(Q.
TextureSize.X*Ɓ.Position.Value.X,Q.TextureSize.Y*Ɓ.Position.Value.Y+ɢ);Ē.Add(ɗ);ɟ=true;}}MySprite Ɏ=new MySprite();var ĕ=new ñ();if
(Ȏ.Ȕ.Ȧ("control")&&(ɔ.Count>0)){Ɏ=ȹ(ɔ[0],k,Q,ĕ).FirstOrDefault();if(Ɏ.Position.HasValue){ĕ.ò=Ɏ.Position.Value;ĕ.õ=true;}
if(ɉ.HasValue){var ę=û.Ş();var ɏ=ğ(ę,ɉ.Value/350f);var ɐ=ğ(ę,Ɉ.Value/350f);Vector2 Ė,D;if(N(ɐ,Q,k.Value,true,out Ė)&&N(ɏ,Q
,k.Value,true,out D)){var ė=D-Ė;var Ę=new MySprite(SpriteType.TEXTURE,"SquareHollow",Ė+ė/2,ė,new Color(30,100,0,20));Ē.
Add(Ę);ĕ.ö=true;ĕ.ó=Ė;ĕ.ô=D;}}}foreach(var I in ʍ.ɶ){var R=ȹ(I,k,Q,ĕ);if(R.Any()){ɟ=true;Ē.AddRange(R);}}foreach(var I in ʍ
.ɺ){var į=new MyTuple<Vector3D,Vector3D>(I.Item1,I.Item2);var ĺ=ʌ(į,2f,new Color(I.Item3),Q,k??Vector3D.Zero,new PlaneD(Q
.GetPosition(),Q.WorldMatrix.Forward));if(ĺ.Data!=null){Ē.Add(ĺ);ɟ=true;}}if(ĕ.õ){var ɑ=MySprite.CreateText(ɚ(),"Debug",
Color.Goldenrod,0.6f,TextAlignment.LEFT);ɑ.Position=Vector2.One*10f;Ē.Add(ɑ);bool Ľ=false;ʒ.Ɲ(ĕ.ò,ref ʐ,ʍ);ʐ|=Ľ;ʒ.Ƣ(Ē,Q,ĕ.ò,
false);Ē.Add(Ɏ);ɟ=true;if(ɔ.Count>1){var ɒ=ɔ[1];var ľ=ʍ.ɾ;if((ľ!=null)&&((ľ.ǲ==0)||!ʍ.ɽ.Contains(ľ.ǲ))){ɒ.Ǔ=ľ.Ǭ;if(ʒ.þ.
ContainsKey(ľ.Ǭ))ɒ.ǐ=ʒ.þ[ľ.Ǭ].Ǉ(Vector2.Zero,Vector2.One*30);}Ē.AddRange(ȹ(ɒ,k,Q,ĕ));var ę=Color.GhostWhite;ę.A=10;ę.Shade(0.5f);ʍ.
ɺ.Add(new MyTuple<Vector3D,Vector3D,Vector4>(ɔ[0].Ǎ,ɔ[1].Ǎ,ę));}}}if(ɟ){Q.ContentType=ContentType.TEXT_AND_IMAGE;Q.
ContentType=ContentType.SCRIPT;}}}RayD ɓ;List<ǌ>ɔ=new List<ǌ>(2);public override void Ɇ(IMyShipController ę,bool ł){ɔ.Clear();ʑ=
null;if(!ł){ɉ=null;Ɉ=null;return;}if(û.œ("q"))ʏ=0;if(!û.Ŏ("q")){ɇ();}else{ʏ+=û.ũ().X;}Ɋ("e");float ɕ=350;var ɖ=ɣ/ɕ;if(ę.
CubeGrid.GridSizeEnum==MyCubeSize.Large){ɓ=new RayD(ę.GetPosition()+ę.WorldMatrix.Up*(0.5f)+ę.WorldMatrix.Backward*(0.12f),ę.
WorldMatrix.Forward);}else{ɓ=new RayD(ę.GetPosition()+ę.WorldMatrix.Up*(0.46f)+ę.WorldMatrix.Backward*(0.28f),ę.WorldMatrix.Forward
);}var Ŀ=new ǌ();Ŀ.Ǎ=ğ(ę,ɖ);Ŀ.Ǐ="Triangle";Ŀ.Ǒ=new Vector2(7f,10f);Ŀ.Ɨ=new Color(1f);Ŀ.ǒ=6f;ɔ.Add(Ŀ);if(ʍ.ɽ.Any()){if(ʍ.ɲ
.HasValue){var Ń=ʍ.ɲ.Value;if(ʗ(ɓ,ɖ,Ń,ę)){Ŀ.Ǎ=ʑ.Value;Ŀ.Ǐ="Arrow";Ŀ.Ɨ=Color.CadetBlue;Ŀ.Ǒ=new Vector2(12f,12f);Ŀ.ǒ=ʏ>0?0f
:3.14f;Ŀ.Ǔ=$"    Go ({(ɓ.Position-ʑ.Value).Length():f1} m)";ɔ.Add(Ŀ);}}}if(ʅ!=null){var M=ʖ(ę,ɖ);var í=M.Position+M.
Direction*2000;var Ô=ʆ.FirstOrDefault(ĝ=>ĝ.CanScan(í));if(Ô!=null){var Ğ=Ô.Raycast(í);if(!Ğ.IsEmpty()){ʅ.Invoke(Ğ);ʅ=null;}}}}
Vector3D ğ(IMyEntity ę,Vector2 Ġ){return ę.WorldMatrix.Translation+ę.WorldMatrix.Forward*10+Ġ.X*ę.WorldMatrix.Right+Ġ.Y*ę.
WorldMatrix.Down;}public Vector3D ġ(){if(ɔ.Count>1)return ɔ[1].Ǎ;else return ɔ[0].Ǎ;}public override RayD ʖ(IMyShipController ę,
Vector2 Ġ){var I=ğ(ę,Ġ);return new RayD(ɓ.Position,Vector3D.Normalize(I-ɓ.Position));}public override bool N(Vector3D O,
IMyTextSurface Q,Vector3D k,bool Ģ,out Vector2 R){R=Vector2.Zero;var ģ=Q as IMyTextPanel;if(ģ==null)return false;var Ĥ=O;var ĥ=k;bool
Ħ=ģ.CubeGrid.GridSizeEnum==MyCubeSize.Large;var ħ=Vector2.One*Q.TextureSize.X/((Ħ?2.5f:0.5f)*0.87f);var Ĩ=ģ.WorldMatrix.
Translation+ģ.WorldMatrix.Forward*(Ħ?2.45f:0.5f)/2f;var ĩ=new PlaneD(Ĩ,ģ.WorldMatrix.Forward);var Ī=new RayD(Ĥ,Vector3D.Normalize(ĥ
-Ĥ));var ī=Ī.Intersects(ĩ);if(ī.HasValue&&(Vector3D.Dot(Ī.Direction,-ĩ.Normal)>0)){float Ĭ=Q.TextureSize.X/2f;float Į=Q.
TextureSize.Y/2f;var ĭ=Ī.Position+Vector3D.Normalize(Ī.Direction)*ī.Value;if(Ģ||((ĭ-Ĩ).Length()<(Ħ?1.25f:0.25f)*1.41)){var ě=
Vector3D.TransformNormal(ĭ-ģ.WorldMatrix.Translation,MatrixD.Transpose(ģ.WorldMatrix));Vector2 ę=new Vector2((float)ě.X,(float)ě
.Y);ę*=ħ;R=new Vector2(Ĭ+ę.X,Į-ę.Y);return true;}}return false;}}class Ċ:ɰ{bool ċ;ĸ Č;IMyTextSurface č;float Ď=0.3f;
public Ċ(ƍ ă,ĸ ď,ɵ Đ,IMyTextSurface Q,ɧ Ă,ø đ):base(Ă,ă,Đ,.7f,đ){Č=ď;č=Q;}public override void Ʌ(){using(var Ē=č.DrawFrame()){
Ē.Add(new MySprite());}č.ContentType=ContentType.TEXT_AND_IMAGE;č.ContentType=ContentType.SCRIPT;}public Vector3D ē(){
return Č.À.Translation;}public override void Ƣ(RayD?j,Vector3D?k){using(var Ē=č.DrawFrame()){var Ĕ=Č.À;var J=new PlaneD(Ĕ.
Translation,Ĕ.Forward);if(ŀ){Ē.AddRange(Č.F(this,č));}var ĕ=new ñ();if(ċ){ĕ.ò=ɣ*Ď+č.TextureSize/2f;ĕ.õ=true;if(ɉ.HasValue){var Ė=Ɉ.
Value*Ď+č.TextureSize/2f;var D=ɉ.Value*Ď+č.TextureSize/2f;var ė=D-Ė;var Ę=new MySprite(SpriteType.TEXTURE,"SquareHollow",Ė+ė/
2,ė,new Color(30,100,0,20));Ē.Add(Ę);ĕ.ö=true;ĕ.ó=Ė;ĕ.ô=D;}}foreach(var I in ʍ.ɶ){var R=ȹ(I,k,č,ĕ);if(R.Any()){Ē.AddRange
(R);}}foreach(var I in ʍ.ɺ){var į=new MyTuple<Vector3D,Vector3D>(I.Item1,I.Item2);var ĺ=ʌ(į,2f,new Color(I.Item3),č,k??
Vector3D.Zero,J);if(ĺ.Data!=null){Ē.Add(ĺ);}}var ę=û.Ş();if(ę!=null){Vector2 Ļ;var ļ=new MySprite(SpriteType.TEXTURE,
"CircleHollow",size:Vector2.One*15,color:Color.Yellow);N(ę.WorldMatrix.Translation,č,k.Value,false,out Ļ);ļ.Position=Ļ;Ē.Add(ļ);bool Ľ
=false;if(ŀ){var Ķ=MySprite.CreateText(Č.Ĺ,"Debug",Color.White,ʎ,TextAlignment.LEFT);Ķ.Position=č.TextureSize/15f;Ē.Add(Ķ
);}else{ʒ.Ɲ(ĕ.ò,ref ʐ,ʍ);ʐ|=Ľ;ʒ.Ƣ(Ē,č,ĕ.ò,false);}}if(ċ){var ľ=ʍ.ɾ;if((ľ!=null)&&((ľ.ǲ==0)||!ʍ.ɽ.Contains(ľ.ǲ))){if(ʒ.þ.
ContainsKey(ľ.Ǭ))Ē.AddRange(ʒ.þ[ľ.Ǭ].Ǉ(ĕ.ò,Vector2.One*30));}else{var Ŀ=new MySprite(SpriteType.TEXTURE,"Triangle",size:Vector2.One
*5,color:Color.AliceBlue);Ŀ.Size=new Vector2(7f,10f);Ŀ.Color=new Color(1f);Ŀ.RotationOrScale=6f;Ŀ.Position=ĕ.ò;Ē.Add(Ŀ);}
}}č.ContentType=ContentType.TEXT_AND_IMAGE;č.ContentType=ContentType.SCRIPT;}bool ŀ;public override void Ɇ(
IMyShipController Ł,bool ł){ʑ=null;if(!ł){ɉ=null;Ɉ=null;ċ=false;return;}if(û.œ("q"))ŀ=!ŀ;if(ŀ&&û.œ("e")){if(ʍ.ɱ().Any())Č.z(ʍ.ɱ().First()
.Ǎ);else Č.z();}ċ=!ŀ;if(ɇ()){if(ŀ){var L=new Vector2(û.ũ().Y,û.ũ().X);Č.Ç(ʀ);Č.Í(L,Ł.WorldMatrix.Up);}else{ɣ=Vector2.
Clamp(ɣ,-č.TextureSize/2/Ď,č.TextureSize/2/Ď);}}if(ċ){Ɋ("e");if(ʍ.ɽ.Any()){if(ʍ.ɲ.HasValue){var Ń=ʍ.ɲ.Value;var º=Č.À.
Translation;var ń=new RayD(º,Č.À.Forward);if(ʗ(ń,ɣ,Ń,Ł)){var Ŀ=new ǌ();Ŀ.Ǎ=ʑ.Value;Ŀ.Ǐ="Arrow";Ŀ.Ɨ=Color.CadetBlue;Ŀ.Ǒ=new Vector2(
12f,12f);Ŀ.ǒ=ʏ>0?0f:3.14f;Ŀ.Ǔ=$"    Go ({(º-ʑ.Value).Length():f1} m)";ʍ.ɶ.Add(Ŀ);}}}}}public override RayD ʖ(
IMyShipController ę,Vector2 Ġ){var Ņ=new Vector2(Ġ.X/(č.TextureSize.X/2f),-Ġ.Y/(č.TextureSize.Y/2f));var ņ=new Vector3D(Ņ*Ď,1f);var Ň=
Vector3D.Transform(ņ,Č.Â);var ň=Vector3D.Rotate(Ň,Č.À);return new RayD(Č.À.Translation,Vector3D.Normalize(ň));}private void ŉ(
MatrixD Ŋ){}List<PlaneD>ŋ;IEnumerable<MySprite>Ō(MatrixD Ĳ,Vector3D k){var ę=new Color(0,0,30*2,45*2);var ĳ=Ĳ.Translation;var Ĕ
=Č.À;var J=new PlaneD(Ĕ.Translation,Ĕ.Forward);for(float Ĉ=0;Ĉ<2*3.14;Ĉ+=3.14f/4f){var İ=new RayD(Ĳ.Translation,Ĳ.Forward
*Math.Sin(Ĉ)+Ĳ.Left*Math.Cos(Ĉ));if(true){var I=İ.Position+İ.Direction*10000;yield return ʌ(new MyTuple<Vector3D,Vector3D
>(ĳ,I),1f,ę,č,k,J);}}}IEnumerable<MySprite>ı(MatrixD Ĳ,Vector3D k){var ę=new Color(0,0,30*2,45*2);var Ĕ=Č.À;var J=new
PlaneD(Ĕ.Translation,Ĕ.Forward);var ĳ=Ĳ.Translation;var Ĵ=Ĳ.Left;var ĵ=Ĳ.Forward;var L=100;yield return ʌ(new MyTuple<Vector3D
,Vector3D>(ĳ+Ĵ*L,ĳ-Ĵ*L),1f,ę,č,k,J);yield return ʌ(new MyTuple<Vector3D,Vector3D>(ĳ+ĵ*L,ĳ-ĵ*L),1f,ę,č,k,J);var Ķ=MySprite
.CreateText(L.ToString(),"Debug",ę*2f,0.3f,TextAlignment.CENTER);Vector2 I;N(ĳ+ĵ*L+Ĳ.Up*L/5,č,k,true,out I);Ķ.Position=I;
yield return Ķ;var ķ=new List<Vector3D>();Ŗ.ŗ(30,L,Ĳ,0,ķ);for(int Ĉ=0;Ĉ<ķ.Count-1;Ĉ++){yield return ʌ(new MyTuple<Vector3D,
Vector3D>(ķ[Ĉ],ķ[Ĉ+1]),1f,ę,č,k,J);}yield return ʌ(new MyTuple<Vector3D,Vector3D>(ķ.Last(),ķ[0]),1f,ę,č,k,J);}public override
bool N(Vector3D O,IMyTextSurface Q,Vector3D k,bool Ģ,out Vector2 R){return Č.N(O,Q,out R);}}class ĸ{public string Ĺ;
IMyEntity Ě;public ĸ(IMyEntity q){Ě=q;var y=q.WorldMatrix;z(y.Translation);}public void z(Vector3D?ª=null){Ä=ª??Ě.GetPosition();
var µ=Ě.WorldMatrix;var º=Ä+µ.Up*500+µ.Backward*300;Å=(float)(º-Ä).Length();À=MatrixD.CreateFromDir(Ä-º,µ.Up);À.Translation
=º;Á=MatrixD.CreatePerspectiveFieldOfView(1.05f,1f,2f,5000f);Â=MatrixD.Invert(Á);Ã=MatrixD.Invert(À)*Á;}public MatrixD À;
public MatrixD Á;public MatrixD Â;public MatrixD Ã;Vector3D Ä;public float Å=700f;float Æ=150;public void Ç(Vector3 È){var É=
3f*(Å/Æ);var Ê=À.Translation;var Ë=È*É;À.Translation+=À.Up*Ë.Y+À.Left*Ë.X;var Ì=Ä;Ì+=À.Up*Ë.Y+À.Left*Ë.X;Å-=Ë.Z;Å=Math.Max
(Æ,Å);Ä=Ì;}public void Í(Vector2 Î,Vector3D?Ï=null){var Ð=Ä;var Ñ=0.1f*(Å/Æ);var M=Vector3D.Normalize(Ð-À.Translation);
var Ê=Ð-M*Å;var Ó=Ï??À.Up;À=MatrixD.CreateFromDir(M,Ó);À.Translation=Ê+À.Up*Î.Y*Ñ+À.Left*Î.X*Ñ;Ã=MatrixD.Invert(À)*Á;Ĺ=
$"Focus point from camera: {Å:f2}m";Ĺ+=$"\nFocus point from origin: {(Ě.GetPosition()-Ä).Length():f2}m";}public MyTuple<Vector3D,Vector3D>Ò(){var I=Ä;var U
=new PlaneD(Ě.WorldMatrix.Translation,Ě.WorldMatrix.Up);var B=U.DistanceToPoint(I);var D=I+Ě.WorldMatrix.Up*B;return new
MyTuple<Vector3D,Vector3D>(I,D);}public IEnumerable<MySprite>F(ɰ G,IMyTextSurface H){var I=Ä;var J=new PlaneD(À.Translation,À.
Forward);yield return G.ʌ(Ò(),1f,Color.Green,H,Vector3D.Zero,J);yield return G.ʌ(new MyTuple<Vector3D,Vector3D>(I,I+Ě.
WorldMatrix.Left*100),1f,Color.Red,H,Vector3D.Zero,J);yield return G.ʌ(new MyTuple<Vector3D,Vector3D>(I,I+Ě.WorldMatrix.Forward*100
),1f,Color.Blue,H,Vector3D.Zero,J);var K=Ě.WorldMatrix.Translation-Ä;var L=K.Length();if(L>500){var M=K/L;yield return G.
ʌ(new MyTuple<Vector3D,Vector3D>(I+M*400,I+M*500),1f,Color.Yellow,H,Vector3D.Zero,J);}}public bool N(Vector3D O,
IMyTextSurface Q,out Vector2 R){var S=Q.TextureSize/2f;if(Vector3D.Dot(Vector3D.Normalize(O-À.Translation),À.Forward)<=0){R=Vector2.
Zero;var U=new PlaneD(À.Translation,À.Forward);ŷ.ƀ($"Culled pt D: {U.DistanceToPoint(O):f2}");return false;}var V=Vector3D.
Transform(O,Ã);R=new Vector2(((float)V.X+1f)*S.X,(1-(float)V.Y)*S.Y);return true;}}int W;List<ɰ>X=new List<ɰ>();void Y(){if(++W>X
.Count-1)W=0;X.ForEach(Z=>Z.Ʌ());}void f(ref RayD?j,ref Vector3D?k,IMyShipController o){Ș=ȳ.ȵ;List<IMyCameraBlock>A=new
List<IMyCameraBlock>();GridTerminalSystem.GetBlocksOfType(A,Ô=>Ô.IsActive);var Õ=A.FirstOrDefault();if(Õ!=null){Ș=ȳ.Ȥ;k=Õ.
WorldMatrix.Translation+Õ.WorldMatrix.Forward*(Õ.CubeGrid.GridSize/2f-0.05f)+Õ.WorldMatrix.Right*0.005f+Õ.WorldMatrix.Down*0.005f;j
=new RayD(k.Value,Õ.WorldMatrix.Forward);}else if(o!=null){if(o.CubeGrid.GridSizeEnum==MyCubeSize.Large){k=o.GetPosition(
)+o.WorldMatrix.Up*(0.5f)+o.WorldMatrix.Backward*(0.12f);}else{k=o.GetPosition()+o.WorldMatrix.Up*(0.46f)+o.WorldMatrix.
Backward*(0.28f);}j=new RayD(k.Value,o.WorldMatrix.Forward);Ș=ȳ.ȴ;}else{if(ȱ.Count>0){List<MyDetectedEntityInfo>î=new List<
MyDetectedEntityInfo>();foreach(var ï in ȱ){ï.DetectedEntities(î);foreach(var ð in î){if(ð.Type==MyDetectedEntityType.CharacterHuman){k=ð.
Position+ð.Orientation.Up*0.75f+ð.Orientation.Forward*0.1f;Ș=ȳ.ȥ;break;}}if(k.HasValue)break;}}}}struct ñ{public Vector2 ò;
public Vector2 ó;public Vector2 ô;public bool õ;public bool ö;}class ø{int ù;float ú;ƍ û;Vector2 ü;Vector2 ý;public Dictionary
<string,ǂ>þ;ɧ ÿ;public ø(IMyTextSurface Ā,float ā,ɧ Ă,ƍ ă,Dictionary<string,ǂ>Ą,List<string>ą){û=ă;ú=ā;ÿ=Ă;ù=(int)Ā.
MeasureStringInPixels(new StringBuilder("HeightMeasure"),"Debug",ā).Y;ü=Ā.TextureSize;ý=Ā.SurfaceSize;â=ą;Ć=new ǂ(@"Sprites/default=ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4199219,SizeY=0.4199219,PosX=0.009758592,PosY=-0.2696313,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.6398172,PosY=0.02082145,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.3270218,PosY=0.3403192,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.02092969,PosY=0.6419433,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=-0.6091293,PosY=0.04316401,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=-0.3052711,PosY=0.3425535,Rotation=-0.8000001,Type=TEXTURE"
);þ=Ą;ć();}ǂ Ć;void ć(){var ĉ=new RectangleF((ü-ý)/2f,ý);int Ĉ=0;var é=ù*1.2f*1.5f;var è=é;Color Ö=Color.ForestGreen;List
<string>Ø=new List<string>(){"wait-for-signal","attack","move","jab","pnode","ram","dock","land","su launch","repeat"};
foreach(var u in Ø){Vector2 Ù=ĉ.Position+new Vector2(ý.X-(è+è*1.2f*Ĉ),ý.Y*0.95f);Action Ú=()=>ÿ.ɨ=u;List<MySprite>Û;if(þ.
ContainsKey(u))Û=þ[u].Ǉ(Ù,new Vector2(è,é));else Û=Ć.Ǉ(Ù,new Vector2(è,é));var Ü=new ƨ(Ù,new Vector2(è,é),Û,Ú,null);var Ý=u;Ü.Ƹ(new
Vector2(0,é+ù/2),Ý,ú,Ö);ơ.Add(Ü);Ĉ++;}}List<ƨ>Þ=new List<ƨ>();public void ß(Vector2 I){foreach(var à in Þ){if(à.ƾ(I))à.ƽ();}ŷ.Ĝ
("CloseControls");Þ.Clear();}public Action<string,Ǩ>á;List<string>â;void ã(Vector2 ä,bool å,bool æ,Ǩ ç){var è=ü.X*0.05f*
1.2f;var é=è;var ê=ç?.ǫ??â;var L=è*Math.Max(1,ê.Count/5f);if(å&&!Þ.Any()){int ë=0;double ì=2*Math.PI/ê.Count;foreach(var u
in ê){Action Ú=()=>á?.Invoke(u,ç);var Ĥ=ä+new Vector2((float)Math.Cos(ì*ë)*L,(float)Math.Sin(ì*ë)*L);ë++;List<MySprite>Û;
if(þ.ContainsKey(u))Û=þ[u].Ǉ(Ĥ,new Vector2(è,é));else Û=Ć.Ǉ(Ĥ,new Vector2(è,é));var à=new ƨ(Ĥ,new Vector2(è,é),Û,Ú,null);Þ
.Add(à);à.Ƹ(new Vector2(0,é+ù/2),u,ú,Color.ForestGreen);}}else if(æ){ß(ä);}}public void Ɲ(Vector2 ƞ,ref bool Ľ,ɵ Đ){bool
Ɵ=û.ő("e");ã(ƞ,û.Ŏ("d"),û.ő("d"),Đ.ɾ);foreach(var Ü in ơ.Union(Þ)){if(Ü.ƾ(ƞ)){if(Ɵ){Ľ|=Ü.ƽ();}}}}List<MySprite>Ơ=new List
<MySprite>();List<ƨ>ơ=new List<ƨ>();public void Ƣ(MySpriteDrawFrame Ē,IMyTextSurface H,Vector2 ƞ,bool ƣ){var Ƥ=H;var ĉ=
new RectangleF((Ƥ.TextureSize-Ƥ.SurfaceSize)/2f,Ƥ.SurfaceSize);Color Ö=Color.ForestGreen;Ơ.Clear();MySprite ƥ=MySprite.
CreateText(ÿ.ɨ,"Debug",Ö,ú,TextAlignment.CENTER);ƥ.Position=ĉ.Position+new Vector2(Ƥ.SurfaceSize.X/2f,30+ù/2f);Ơ.Add(ƥ);foreach(
var Ü in ơ.Union(Þ)){Ơ.AddRange(Ü.ƿ());}Ē.AddRange(Ơ);}public void Ƣ(IMyTextSurface H,Vector2 ƞ,bool ƣ){using(var Ē=H.
DrawFrame()){Ƣ(Ē,H,ƞ,ƣ);}H.ContentType=ContentType.TEXT_AND_IMAGE;H.ContentType=ContentType.SCRIPT;}public List<MySprite>Ʀ(){
return Ơ;}}class ƨ{Vector2 Ƨ,Ɯ,ĳ;List<MySprite>Ǝ;Action Ə;Action Ɛ;Ɣ Ƒ;Ɣ ƒ;bool Ɠ;class Ɣ{public string ƕ;public Vector2 Ɩ;
public Color Ɨ;public float Ƙ;}public ƨ(Vector2 Ĥ,Vector2 ė,MySprite ƙ,Action Ə=null,Action Ɛ=null){ƙ.Position=Ĥ;ƙ.Size=ė;Ǝ=
new List<MySprite>(){ƙ};Ž(Ĥ,ė,Ə,Ɛ);}public ƨ(Vector2 Ĥ,Vector2 ė,List<MySprite>ƚ,Action Ə=null,Action Ɛ=null){Ǝ=ƚ;Ž(Ĥ,ė,Ə,Ɛ
);}void Ž(Vector2 Ĥ,Vector2 ė,Action Ə,Action Ɛ){ĳ=Ĥ;Ƨ=Ĥ-ė/2;Ɯ=Ĥ+ė/2;this.Ə=Ə;this.Ɛ=Ɛ;}public void Ƹ(Vector2 ƹ,string ƺ,
float ƶ,Color ƻ){ƒ=new Ɣ(){Ɨ=ƻ,ƕ=ƺ,Ɩ=new Vector2(Ƨ.X+ƹ.X,ĳ.Y-ƹ.Y),Ƙ=ƶ};}public void Ƽ(Vector2 ƹ,string ƺ,float ƶ,Color ƻ){Ƒ=
new Ɣ(){Ɨ=ƻ,ƕ=ƺ,Ɩ=new Vector2(Ƨ.X+ƹ.X,ĳ.Y-ƹ.Y),Ƙ=ƶ};}public bool ƽ(){if(Ə!=null){Ə.Invoke();return true;}return false;}
public bool ƾ(Vector2 ƞ){bool R=(ƞ.X>Ƨ.X)&&(ƞ.X<Ɯ.X)&&(ƞ.Y>Ƨ.Y)&&(ƞ.Y<Ɯ.Y);if(R){Ɛ?.Invoke();}Ɠ=R;return R;}public IEnumerable
<MySprite>ƿ(){if(Ɠ){foreach(var ĝ in Ǝ){var ǀ=ĝ;ǀ.Size*=1.1f;yield return ǀ;}}else{foreach(var ĝ in Ǝ)yield return ĝ;}if(
Ƒ!=null){var ǁ=MySprite.CreateText(Ƒ.ƕ,"Debug",Ƒ.Ɨ,Ƒ.Ƙ,TextAlignment.LEFT);ǁ.Position=Ƒ.Ɩ;yield return ǁ;}if(Ɠ&&(ƒ!=null)
){var ǁ=MySprite.CreateText(ƒ.ƕ,"Debug",ƒ.Ɨ,ƒ.Ƙ,TextAlignment.LEFT);ǁ.Position=ƒ.Ɩ;yield return ǁ;}}}class ǂ{public List<
MySprite>ǃ;public List<List<MySprite>>Ǆ;public ǂ(string ǅ){var ǆ=ƫ(ǅ);if(ǆ.Count>0)ǃ=ǆ[0].Item2;}public List<MySprite>Ǉ(Vector2
Ĥ,Vector2 Ʒ){var R=new List<MySprite>();foreach(var Ɓ in ǃ){var ĝ=Ɓ;var Ʃ=new Vector2(Ɓ.Position.Value.X*Ʒ.X/2f,Ɓ.
Position.Value.Y*Ʒ.Y/2f);ĝ.Position=Ĥ+Ʃ;var ƪ=Ɓ.Size.Value;ĝ.Size=new Vector2(ƪ.X>1?ƪ.X:ƪ.X*Ʒ.X,ƪ.Y>1?ƪ.Y:ƪ.Y*Ʒ.Y);if(Ɓ.Type==
SpriteType.TEXT)ĝ.RotationOrScale=Ɓ.RotationOrScale*(Ʒ.Y);R.Add(ĝ);}return R;}List<MyTuple<string,List<MySprite>>>ƫ(string Ƭ){var
ƭ=Ƭ;var R=new List<MyTuple<string,List<MySprite>>>();var Ʈ=ƭ.Split('\n').ToDictionary(Ɓ=>Ɓ.Split('=')[0],Ɓ=>string.Join(
"=",Ɓ.Split('=').Skip(1)));foreach(var Ư in Ʈ.Where(Z=>Z.Key.Contains("Sprites"))){var ư=Ư.Key.Split('/')[1];if(!string.
IsNullOrEmpty(Ư.Value)){var Ǝ=new List<MySprite>();R.Add(new MyTuple<string,List<MySprite>>(ư,Ǝ));var Ʊ=Ư.Value.Split(new[]{'|'},
StringSplitOptions.RemoveEmptyEntries);foreach(string Ʋ in Ʊ){var Ƴ=Ʋ.Split(',').ToDictionary(Ɓ=>Ɓ.Split('=')[0],Ɓ=>Ɓ.Split('=')[1]);Color
ę=new Color(byte.Parse(Ƴ["ColorR"]),byte.Parse(Ƴ["ColorG"]),byte.Parse(Ƴ["ColorB"]),byte.Parse(Ƴ["ColorA"]));MySprite ƴ;
SpriteType Ƶ;if(Enum.TryParse(Ƴ["Type"],out Ƶ)&&(Ƶ==SpriteType.TEXT)){ƴ=MySprite.CreateText(Ƴ["Brush"],"Debug",ę);}else{Vector2 ƪ=
new Vector2(float.Parse(Ƴ["SizeX"]),float.Parse(Ƴ["SizeY"]));var S=ƪ;ƴ=new MySprite(SpriteType.TEXTURE,Ƴ["Brush"],size:S,
color:ę);}ƴ.Position=new Vector2(float.Parse(Ƴ["PosX"]),float.Parse(Ƴ["PosY"]));var ƶ=float.Parse(Ƴ["Rotation"]);ƴ.
RotationOrScale=ƶ;Ǝ.Add(ƴ);}}}return R;}}ƍ ƛ;class ƍ{public Vector2 Ŝ=new Vector2();List<IMyShipController>ŝ;Func<int>ō;public
IMyShipController Ş(){return Š;}public IMyShipController ş(){return š;}IMyShipController Š;IMyShipController š;Vector3 Ţ;Vector2 ţ;float
Ť;public void ť(){foreach(var ĝ in é)ĝ.Ų();Š=ŝ.Where(ę=>ę.IsUnderControl).FirstOrDefault();if(Š!=null){Ţ=Š.MoveIndicator;
Ţ.X=-Ţ.X;Ţ.Z=-Ţ.Z;if(!(Š is IMyRemoteControl))š=Š;}else Ţ=Vector3.Zero;Ť=Š?.RollIndicator??0f;ţ=Š?.RotationIndicator??
Vector2.Zero;}public Vector3 Ŧ(){return Ţ;}public Vector3 ŧ(ref MatrixD Ũ){Vector3 R=new Vector3();if(Ȏ.Ȕ.Ȧ(
"ignore-user-thruster"))return R;if((Š!=null)&&(Š.MoveIndicator!=Vector3.Zero))return Vector3D.TransformNormal(Š.MoveIndicator,Ũ*MatrixD.
Transpose(Š.WorldMatrix));return R;}public Vector2 ũ(){return ţ;}public float Ū(){return Ť;}class ū{public string Ŭ;public int ŭ;
public int Ů{get;private set;}public int ů;public int Ű;public int ű;public void Ų(){if(Ů!=ů){ŷ.Ĝ(
$"Key state [{Ŭ}]: {Ů} -> {ů}");Ů=ů;}if(Ű!=ű){ŷ.Ĝ($"Key state d tap [{Ŭ}]: {Ű} -> {ű}");Ű=ű;}}}List<ū>é;public ƍ(List<IMyShipController>ś,Func<int>ō){
ŝ=ś;this.ō=ō;é=new List<ū>();é.Add(new ū{Ŭ="spacebar"});é.Add(new ū{Ŭ="c"});é.Add(new ū{Ŭ="e"});é.Add(new ū{Ŭ="q"});é.Add
(new ū{Ŭ="w"});é.Add(new ū{Ŭ="s"});é.Add(new ū{Ŭ="a"});é.Add(new ū{Ŭ="d"});}public bool Ŏ(string ŏ){if(Š!=null){bool Ő=
false;if((ŏ=="spacebar")&&(Ţ.Y>0))Ő=true;if((ŏ=="c")&&(Ţ.Y<0))Ő=true;if((ŏ=="e")&&(Ť>0))Ő=true;if((ŏ=="q")&&(Ť<0))Ő=true;if((
ŏ=="w")&&(Ţ.Z<0))Ő=true;if((ŏ=="s")&&(Ţ.Z>0))Ő=true;if((ŏ=="a")&&(Ţ.X>0))Ő=true;if((ŏ=="d")&&(Ţ.X<0))Ő=true;return Ő;}
return false;}public bool ő(string ŏ){var Œ=é.First(é=>é.Ŭ==ŏ);if(Ŏ(ŏ)){if(Œ.Ů==0){Œ.ů=1;Œ.ŭ=ō();}if(Œ.Ů==2){Œ.ů=0;}return
false;}else{if((Œ.Ů==1)||(Œ.Ů==2)){Œ.ů=0;return true;}}return false;}public bool œ(string ŏ){var Ŕ=Ŏ(ŏ);var ŕ=ō();var Œ=é.
First(é=>é.Ŭ==ŏ);if(Ŕ){if(Œ.Ű==0){Œ.ű=1;Œ.ŭ=ŕ;}if(Œ.Ű==2){Œ.ű=0;return true;}}else{if(ŕ-Œ.ŭ<30){if(Œ.Ű==1){Œ.ű=2;}}else{Œ.ű=0
;}}return false;}}static class Ŗ{public static List<Vector3D>ŗ(int Ĉ,float L,MatrixD Ē,float é,List<Vector3D>Ř){var ę=Ē.
Translation;for(int ë=0;ë<Ĉ;++ë){var ř=2*(float)Math.PI*ë/Ĉ;Ř.Add(ę+Ē.Left*L*Math.Cos(ř)+Ē.Forward*L*Math.Sin(ř)+Ē.Up*é);}return Ř;
}public static List<Vector3D>Ś(int Ĉ,float L,Vector3D ę,float é,List<Vector3D>Ř){for(int ë=0;ë<Ĉ;++ë){var ř=2*(float)Math
.PI*ë/Ĉ;Ř.Add(new Vector3D(L*Math.Cos(ř)+ę.X,L*Math.Sin(ř)+ę.Y,ę.Z+é));}return Ř;}static void Ƅ(List<Vector3D>Ř,List<
MyTuple<Vector3D,Vector3D,Vector3D,Vector3D>>ƅ,bool Ɔ=false){for(int ë=1;ë<Ř.Count-1;++ë){var Ƃ=Ř[ë]-Ř[0];var ų=Ř[ë+1]-Ř[0];var
Ƈ=Vector3D.Normalize(Vector3D.Cross(Ƃ,ų));if(Ɔ)Ƈ*=-1;ƅ.Add(new MyTuple<Vector3D,Vector3D,Vector3D,Vector3D>(Ř[ë],Ř[0],Ř[ë
+1],-Ƈ));}}public static List<MyTuple<Vector3D,Vector3D,Vector3D,Vector3D>>ƈ(int Ĉ,float L,float é,float Ɖ){var R=new
List<MyTuple<Vector3D,Vector3D,Vector3D,Vector3D>>();var Ɗ=new List<Vector3D>();var Ƌ=new List<Vector3D>();var ę=Vector3D.
Zero;Ś(Ĉ,L,ę,0.0f,Ɗ);if(Ɖ>0.0){L=1f-Ɖ;Ś(Ĉ,L,ę,é*Ɖ,Ƌ);for(int ë=0;ë<Ɗ.Count;ë++){var ƃ=ë+1;if(ƃ>=Ɗ.Count)ƃ=0;var ų=Ƌ[ë]-Ɗ[ë];
var Ƃ=Ƌ[ƃ]-Ɗ[ë];var Ƈ=Vector3D.Normalize(Vector3D.Cross(Ƃ,ų));R.Add(new MyTuple<Vector3D,Vector3D,Vector3D,Vector3D>(Ɗ[ë],Ƌ
[ë],Ƌ[ƃ],Ƈ));R.Add(new MyTuple<Vector3D,Vector3D,Vector3D,Vector3D>(Ɗ[ë],Ƌ[ƃ],Ɗ[ƃ],Ƈ));}Ƅ(Ɗ,R);Ƅ(Ƌ,R,true);}else{var ƌ=
new Vector3D(0,0,é);for(int ë=0;ë<Ɗ.Count;ë++){var ƃ=ë+1;if(ƃ>=Ɗ.Count)ƃ=0;var Ƃ=Ɗ[ƃ]-Ɗ[ë];var ų=ƌ-Ɗ[ë];R.Add(new MyTuple<
Vector3D,Vector3D,Vector3D,Vector3D>(ƌ,Ɗ[ë],Ɗ[ƃ],Vector3D.Normalize(Vector3D.Cross(Ƃ,ų))));}Ƅ(Ɗ,R);}return R;}public static List
<MyTuple<Vector3D,Vector3D,Vector3D,Vector3D>>Ŵ(float L,int Ĉ,Vector3D ę){var R=new List<MyTuple<Vector3D,Vector3D,
Vector3D,Vector3D>>();List<Vector3D>Ř=new List<Vector3D>();for(int ë=0;ë<Ĉ;++ë){float ř=2f*(float)Math.PI*ë/Ĉ;Ř.Add(new Vector3D
(L*Math.Cos(ř)+ę.X,L*Math.Sin(ř)+ę.Y,0));}for(int ë=1;ë<Ř.Count-1;++ë){R.Add(new MyTuple<Vector3D,Vector3D,Vector3D,
Vector3D>(Ř[ë],Ř[0],Ř[ë+1],new Vector3D(0f,0f,1f)));}return R;}}static string ŵ(params Vector3D[]Ŷ){return string.Join(":",Ŷ.
Select(Z=>string.Format("{0}:{1}:{2}",Z.X,Z.Y,Z.Z)));}static class ŷ{static string Ÿ="";static Action<string>Ź;static
IMyTextSurface ź;public static int Ż;static IMyGridTerminalSystem ż;public static void Ž(Action<string>ž,IMyGridTerminalSystem ª,
IMyProgrammableBlock ſ){Ź=ž;ż=ª;ź=(ż.GetBlockWithName("LCD Debug")as IMyTextPanel)??ſ.GetSurface(0);ź.ContentType=ContentType.TEXT_AND_IMAGE
;ź.WriteText("");}public static void ƀ(string Ɓ){if((Ÿ=="")||(Ɓ.Contains(Ÿ)))Ź(Ɓ);}public static void Ĝ(string Ɓ){ź.
WriteText($"{Ż}: {Ɓ}\n",true);}}