string Ver = "0.9.102";

// AutoPillock/Command script (c)cheerkin

Dictionary<string, CustomIcon> customIcons = new Dictionary<string, CustomIcon>() {
    { "wait-for-signal", new CustomIcon(@"Sprites/default=ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01422691,PosY=-0.4898785,Rotation=-1.05,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01869535,PosY=0.4797863,Rotation=-1.05,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE|ColorR=108,ColorG=19,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE")},
    { "wait-10-s", new CustomIcon(@"Sprites/default=ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01422691,PosY=-0.4898785,Rotation=-1.05,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=Circle,SizeX=0.421875,SizeY=0.421875,PosX=0.01869535,PosY=0.4797863,Rotation=-1.05,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=-0.001412868,PosY=-0.7544643,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.02316368,PosY=0.7603574,Rotation=6.250006,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4785156,SizeY=0.1610227,PosX=0.007523775,PosY=-0.02386445,Rotation=7.800012,Type=TEXTURE")},
    { "attack-pos", new CustomIcon(@"Sprites/default=ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=0.4274551,Rotation=-6.250005,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=-0.4617766,Rotation=-9.450017,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=0.4543742,PosY=-0.02162927,Rotation=-7.900011,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.4750742,PosY=0.03422689,Rotation=-4.799999,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=CircleHollow,SizeX=0.8046875,SizeY=0.8046875,PosX=-0.02152109,PosY=-0.01269257,Rotation=-3.049997,Type=TEXTURE")},
    { "attack", new CustomIcon(@"Sprites/default=ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=0.4274551,Rotation=-6.250005,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.02152109,PosY=-0.4617766,Rotation=-9.450017,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=0.4543742,PosY=-0.02162927,Rotation=-7.900011,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1074219,SizeY=0.4935207,PosX=-0.4750742,PosY=0.03422689,Rotation=-4.799999,Type=TEXTURE|ColorR=127,ColorG=0,ColorB=0,ColorA=255,Brush=CircleHollow,SizeX=0.8046875,SizeY=0.8046875,PosX=-0.02152109,PosY=-0.01269257,Rotation=-3.049997,Type=TEXTURE")},
    { "ram", new CustomIcon(@"Sprites/default=ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.8515625,SizeY=0.2754273,PosX=-0.01034999,PosY=-0.03056604,Rotation=5.450003,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.3496094,SizeY=0.1130769,PosX=-0.6672196,PosY=0.2822293,Rotation=5.450003,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.3496094,SizeY=0.1130769,PosX=-0.2427121,PosY=0.6531148,Rotation=5.450003,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=RightTriangle,SizeX=0.2167969,SizeY=0.2167969,PosX=0.4856539,PosY=0.1280661,Rotation=7.000009,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=RightTriangle,SizeX=0.2167969,SizeY=0.2167969,PosX=-0.2181348,PosY=-0.4707129,Rotation=5.400002,Type=TEXTURE|ColorR=105,ColorG=16,ColorB=0,ColorA=255,Brush=Circle,SizeX=0.2207031,SizeY=0.2207031,PosX=0.5772582,PosY=-0.6807323,Rotation=7.25001,Type=TEXTURE")},
    { "jab", new CustomIcon(@"Sprites/default=ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Textures\FactionLogo\Others\OtherIcon_4.dds,SizeX=1,SizeY=1,PosX=0.3091477,PosY=-0.2897394,Rotation=7.100009,Type=TEXTURE|ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.09765625,SizeY=0.242833,PosX=-0.83032,PosY=0.3112738,Rotation=4.349998,Type=TEXTURE|ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.09765625,SizeY=0.242833,PosX=-0.358893,PosY=0.8005747,Rotation=3.599998,Type=TEXTURE|ColorR=117,ColorG=10,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.1210938,SizeY=0.3011129,PosX=-0.6694538,PosY=0.6352402,Rotation=3.949997,Type=TEXTURE")},
    { "su launch", new CustomIcon(@"Sprites/default=ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=Textures\FactionLogo\Others\OtherIcon_22.dds,SizeX=0.7871094,SizeY=0.7871094,PosX=0.01869488,PosY=0.2005055,Rotation=6.600007,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=-0.7119048,PosY=-0.4049762,Rotation=19.49999,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=-0.296334,PosY=-0.6931946,Rotation=20.09998,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=0.4052203,PosY=-0.6708521,Rotation=20.84997,Type=TEXTURE|ColorR=0,ColorG=49,ColorB=78,ColorA=255,Brush=SquareSimple,SizeX=0.2285156,SizeY=0.05278418,PosX=0.7694031,PosY=-0.3826336,Rotation=21.34996,Type=TEXTURE")},
    { "dock", new CustomIcon(@"Sprites/default=ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.6035156,SizeY=0.4225572,PosX=0.01869571,PosY=0.5123563,Rotation=3.149998,Type=TEXTURE|ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.3984375,SizeY=0.2789699,PosX=0.03433549,PosY=0.06550622,Rotation=3.149998,Type=TEXTURE|ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.1640625,SizeY=0.1148699,PosX=-0.3812355,PosY=0.03869534,Rotation=2.299999,Type=TEXTURE|ColorR=0,ColorG=131,ColorB=124,ColorA=255,Brush=SquareSimple,SizeX=0.1640625,SizeY=0.1148699,PosX=0.4275628,PosY=0.036461,Rotation=3.999997,Type=TEXTURE|ColorR=0,ColorG=215,ColorB=40,ColorA=255,Brush=SquareSimple,SizeX=0.390625,SizeY=0.03775207,PosX=0.0321008,PosY=-0.1288738,Rotation=6.300006,Type=TEXTURE|ColorR=0,ColorG=218,ColorB=37,ColorA=255,Brush=RightTriangle,SizeX=0.4140625,SizeY=0.4140625,PosX=0.05444336,PosY=-0.8974556,Rotation=5.500003,Type=TEXTURE")},
    { "follow", new CustomIcon(@"Sprites/default=ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.4199219,SizeY=0.4199219,PosX=0.009758592,PosY=-0.2696313,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.6398172,PosY=0.02082145,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.3270218,PosY=0.3403192,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=0.02092969,PosY=0.6419433,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=-0.6091293,PosY=0.04316401,Rotation=-0.8000001,Type=TEXTURE|ColorR=0,ColorG=9,ColorB=118,ColorA=255,Brush=SquareSimple,SizeX=0.1953125,SizeY=0.1953125,PosX=-0.3052711,PosY=0.3425535,Rotation=-0.8000001,Type=TEXTURE")},
    { "repeat", new CustomIcon(@"Sprites/default=ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=CircleHollow,SizeX=0.9453125,SizeY=0.9453125,PosX=0.01375592,PosY=-0.005937517,Rotation=0,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=Triangle,SizeX=0.2265625,SizeY=0.2265625,PosX=0.5028293,PosY=0.2001027,Rotation=2.599999,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=Triangle,SizeX=0.2265625,SizeY=0.2265625,PosX=-0.512777,PosY=0.002440572,Rotation=3.649998,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=SquareSimple,SizeX=0.2167969,SizeY=0.08215707,PosX=-0.3006274,PosY=0.182839,Rotation=7.25001,Type=TEXTURE|ColorR=0,ColorG=69,ColorB=149,ColorA=255,Brush=SquareSimple,SizeX=0.2167969,SizeY=0.08215707,PosX=0.2771699,PosY=-0.1750945,Rotation=10.45002,Type=TEXTURE")},
    { "land", new CustomIcon(@"Sprites/default=ColorR=255,ColorG=255,ColorB=255,ColorA=255,Brush=SquareSimple,SizeX=0.9140625,SizeY=0.2785904,PosX=0.007524252,PosY=0.6374747,Rotation=15.70004,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.6425781,SizeY=0.6425781,PosX=0.02763247,PosY=-0.03503478,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.6113281,SizeY=0.6113281,PosX=0.02316403,PosY=-0.03503478,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.5839844,SizeY=0.5839844,PosX=0.02316403,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.5644531,SizeY=0.5644531,PosX=0.01869571,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.5351563,SizeY=0.5351563,PosX=0.01869571,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=8,ColorG=131,ColorB=124,ColorA=255,Brush=SquareHollow,SizeX=0.4609375,SizeY=0.4609375,PosX=0.01869571,PosY=-0.02609766,Rotation=21.19996,Type=TEXTURE|ColorR=171,ColorG=171,ColorB=171,ColorA=255,Brush=Triangle,SizeX=0.3457031,SizeY=0.3457031,PosX=0.1482821,PosY=0.03199255,Rotation=19.89998,Type=TEXTURE|ColorR=171,ColorG=171,ColorB=171,ColorA=255,Brush=SquareSimple,SizeX=0.1992188,SizeY=0.06837877,PosX=0.02092969,PosY=-0.2651625,Rotation=23.54993,Type=TEXTURE")},
    //{ "task kind", new CustomIcon(@"Orbital Painter normalized export (command:export)")},
    { "move", new CustomIcon(@"Sprites/default=ColorR=93,ColorG=113,ColorB=0,ColorA=255,Brush=Triangle,SizeX=0.7148438,SizeY=0.7148438,PosX=0.2622281,PosY=-0.00598985,Rotation=1.05,Type=TEXTURE|ColorR=93,ColorG=113,ColorB=0,ColorA=255,Brush=SquareSimple,SizeX=0.40625,SizeY=0.40625,PosX=0.003055453,PosY=-0.5824262,Rotation=0,Type=TEXTURE")}
};

Dictionary<string, string> tasksMapping = new Dictionary<string, string>()
{
    { "jab", "command:create-task:jab:TargetId={targetId}" },
    { "ram", "command:create-task:ram:TargetId={targetId}" },
    { "attack-pos", "command:create-task:attack:Name=static attack:{wPos}" },
    { "su launch", "su:command:create-task:ram:{wPos}" },
    { "attack", "command:create-task:attack:TargetId={targetId}" },
    { "follow-enemy?", "" },
    { "remove", "command:remove-task:{targetId}" }, // what if I select many units and remove task?
    { "dock", "command:create-task:dock:TargetId={targetId}" },
    { "follow", "command:create-task:follow:TargetId={targetId}" },
    { "move", "command:create-task:move:{wPos}" },
    { "wait-for-signal", "command:create-task:wait-for-signal" },
    { "wait-10-s", "command:create-task:wait:Ticks=600" },
    { "repeat", "command:create-task:exec:Name=Repeat,FollowUp=command;repeat" },
    { "land", "command:create-task:land:Name=NG-aligned landing:{wPos}" },
    { "response:free-fire", "command:exec:FollowUp=command;set-response;FreeFire" },
    { "response:attack", "command:exec:FollowUp=command;set-response;Attack" },
    // add here new APck-compatible commands
    { "next", "command:next" }
};

// menu (hold D) for non-hover (no context)
List<string> commandsForNonHover = new List<string>() { "move", "wait-for-signal", "wait-10-s", "repeat", "response:free-fire", "response:attack", "next" };

// context menu (hold D) for static raycasted point hover (diamond mark)
List<string> unitToStaticTargetCommands = new List<string>() { "jab", "ram", "attack-pos", "su launch", "land" };

// context menu (hold D) for datalink target hover (red mark)
List<string> unitToDatalinkTargetCommands = new List<string>() { "jab", "ram", "attack" };

// context menu (hold D) for task node hover
List<string> unitToTaskCommands = new List<string>() { "remove" };

// context menu (hold D) for hovering over another unit while having units selected
List<string> unitToUnitCommands = new List<string>() { "dock", "follow" };

string BlackScreensTag = "[CommanderViewScreen]";

static class Variables
{
    static Dictionary<string, object> variables = new Dictionary<string, object> {
        { "bool", new Variable<bool> { value = false, parser = s => s == "true" } },
        { "centerOffsetY", new Variable<float> { value = 0f, parser = s => float.Parse(s) } },
        { "interface-upscale", new Variable<float> { value = 1f, parser = s => float.Parse(s) } },
        { "int", new Variable<int> { value = 5, parser = s => int.Parse(s) } }
    };
    public static void Set(string key, string value) { (variables[key] as ISettable).Set(value); }
    public static void Set<T>(string key, T value) { (variables[key] as ISettable).Set(value); }
    public static T Get<T>(string key) { return (variables[key] as ISettable).Get<T>(); }
    public interface ISettable
    {
        void Set(string v);
        T1 Get<T1>();
        void Set<T1>(T1 v);
    }
    public class Variable<T> : ISettable
    {
        public T value;
        public Func<string, T> parser;
        public void Set(string v) { value = parser(v); }
        public void Set<T1>(T1 v) { value = (T)(object)v; }
        public T1 Get<T1>() { return (T1)(object)value; }
    }
}

class ToggleManager
{
    static ToggleManager instance;
    ToggleManager() { }
    Action<string> onToggle;
    Dictionary<string, bool> toggleStates;
    ToggleManager(Dictionary<string, bool> states, Action<string> callback)
    {
        onToggle = callback;
        toggleStates = states;
    }
    public static ToggleManager Instance => instance;
    public static void Initialize(Dictionary<string, bool> states, Action<string> callback)
    {
        if (instance == null) instance = new ToggleManager(states, callback);
    }
    public void SetToggle(string key, bool value)
    {
        if (toggleStates[key] != value) Toggle(key);
    }
    public void Toggle(string key)
    {
        toggleStates[key] = !toggleStates[key];
        onToggle(key);
    }
    public bool GetState(string key)
    {
        return toggleStates[key];
    }
}

bool shouldProcessCustomData;
CommandProcessor commandProcessor;

// Global variables that were obfuscated
Logger logger;
InputManager inputManager;
HudData hudData = new HudData();
CurrentCommand currentCommand = new CurrentCommand();
List<IView> views = new List<IView>();
int currentViewIndex;

// Missing class declarations
class Logger
{
    Action<string> echoFunction;
    IMyGridTerminalSystem gridTerminalSystem;
    IMyProgrammableBlock programmableBlock;
    int counter = 0;

    public void Initialize(Action<string> echo, IMyGridTerminalSystem gts, IMyProgrammableBlock pb)
    {
        echoFunction = echo;
        gridTerminalSystem = gts;
        programmableBlock = pb;
    }

    public void Log(string message) => echoFunction?.Invoke(message);
    public void Info(string message) => echoFunction?.Invoke(message);
    public void IncrementCounter() => counter++;
}

class InputManager
{
    List<IMyShipController> controllers;
    Func<int> getRunCount;

    public InputManager(List<IMyShipController> shipControllers, Func<int> runCountGetter)
    {
        controllers = shipControllers;
        getRunCount = runCountGetter;
    }

    public IMyShipController GetCurrentController() => controllers.FirstOrDefault();
    public Vector2 GetMovementInput() => Vector2.Zero; // Simplified
    public Vector3 GetRotationInput() => Vector3.Zero; // Simplified
    public bool IsKeyPressed(string key) => false; // Simplified
    public bool IsKeyHeld(string key) => false; // Simplified
    public bool IsKeyReleased(string key) => false; // Simplified
}

class HudData
{
    public List<ProjectionData> frameProjections = new List<ProjectionData>();
    public List<MySprite> frameSprites = new List<MySprite>();
    public List<MySprite> persistentSprites = new List<MySprite>();
    public List<ProjectionData> persistentProjections = new List<ProjectionData>();
    public List<MyTuple<Vector3D, Vector3D, Vector4>> frameLines = new List<MyTuple<Vector3D, Vector3D, Vector4>>();
    public List<MyTuple<Vector3D, Vector3D, Vector4>> persistentLines = new List<MyTuple<Vector3D, Vector3D, Vector4>>();
    public HashSet<InteractableObject> hoveredObjects = new HashSet<InteractableObject>();
    public InteractableObject currentHoveredObject;
    public List<long> selectedUnitIds = new List<long>();
    public Vector3D? selectionCenter;

    public IEnumerable<ProjectionData> GetSelectedUnits()
    {
        return frameProjections.Where(proj => (proj.interactable?.onCommand != null) && selectedUnitIds.Contains(proj.interactable.sourceId));
    }

    public void UpdateSelectionCenter()
    {
        var selectedUnits = GetSelectedUnits();
        if (selectedUnits.Any())
        {
            var center = new Vector3D();
            foreach (var unit in selectedUnits)
            {
                center += unit.worldPosition;
            }
            center /= selectedUnits.Count();
            selectionCenter = center;
        }
        else selectionCenter = null;
    }
}

class CurrentCommand
{
    public string selectedCommand = "move";
    public List<CommandGroup> commandGroups = new List<CommandGroup>();
}

class CommandGroup
{
    public long targetId;
    public List<ProjectionData> commands = new List<ProjectionData>();
}

interface IView
{
    void ClearDisplay();
    void Render(RayD? cameraRay, Vector3D? cameraPosition);
    void Update(IMyShipController controller, bool isActive);
    void HandleInput();
    void CastRay(List<IMyCameraBlock> cameras, Action<MyDetectedEntityInfo> onHit);
}

class CommandProcessor
{
    Dictionary<string, Action<string[]>> commands;
    public CommandProcessor(Dictionary<string, Action<string[]>> commands)
    {
        this.commands = commands;
    }
    public void ExecuteCommand(string commandName, string[] args)
    {
        this.commands[commandName].Invoke(args);
    }
}

int runCount;
void ProcessCommand(string argument)
{
    runCount++;
    logger.IncrementCounter();
    logger.Log("Run count: " + runCount);
    logger.Log("IGC.Me: " + IGC.Me);
    
    if (shouldProcessCustomData && string.IsNullOrEmpty(argument))
    {
        shouldProcessCustomData = false;
        argument = string.Join(",", Me.CustomData.Trim('\n').Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries).Where(line => !line.StartsWith("//")).Select(line => "[" + line + "]"));
    }
    
    if (!string.IsNullOrEmpty(argument) && argument.Contains(":"))
    {
        var commands = argument.Split(new[] { "],[" }, StringSplitOptions.RemoveEmptyEntries).Select(cmd => cmd.Trim('[', ']')).ToList();
        foreach (var command in commands)
        {
            string[] args = command.Split(new[] { ':' }, StringSplitOptions.RemoveEmptyEntries);
            if (args[0] == "command")
            {
                try
                {
                    this.commandProcessor.ExecuteCommand(args[1], args);
                }
                catch (Exception ex)
                {
                    logger.Log("Unknown command, cmdParts: " + command);
                    throw ex;
                }
            }
            if (args[0] == "toggle")
            {
                ToggleManager.Instance.Toggle(args[1]);
                logger.Info(string.Format("Switching '{0}' to state '{1}'", args[1], ToggleManager.Instance.GetState(args[1])));
            }
        }
    }
}

void PostProcessing() { }

List<IMyCameraBlock> cameras = new List<IMyCameraBlock>();
List<IMySensorBlock> sensors = new List<IMySensorBlock>();
IMyRemoteControl remoteControl;

public enum ViewType { FirstPerson = 0, Cockpit = 1, Character = 2, Camera = 3 }
ViewType currentViewType;
int currentViewIndex = 1;

void Initialize()
{
    if (!string.IsNullOrEmpty(Me.CustomData)) shouldProcessCustomData = true;
    logger.Initialize(line => Echo(line), GridTerminalSystem, Me);
    
    List<IMyBlockGroup> blockGroups = new List<IMyBlockGroup>();
    GridTerminalSystem.GetBlockGroups(blockGroups);
    var hardwareGroup = blockGroups.Where(group =>
    {
        List<IMyProgrammableBlock> programmableBlocks = new List<IMyProgrammableBlock>();
        group.GetBlocksOfType(programmableBlocks);
        return programmableBlocks.Contains(Me);
    }).FirstOrDefault();
    
    if (hardwareGroup == null)
    {
        Runtime.UpdateFrequency = UpdateFrequency.None;
        logger.Log("Can't find hardware group containing this PB, stopping now.");
    }
    else
    {
        List<IMyShipController> shipControllers = new List<IMyShipController>();
        hardwareGroup.GetBlocksOfType(shipControllers);
        inputManager = new InputManager(shipControllers, () => runCount);
        
        List<IMyTextPanel> textPanels = new List<IMyTextPanel>();
        hardwareGroup.GetBlocksOfType(textPanels);
        textPanels.ForEach(panel =>
        {
            panel.ContentType = ContentType.SCRIPT;
            panel.Script = "";
            panel.ScriptBackgroundColor = Color.Transparent;
        });
        
        var hudPanels = textPanels.Where(panel => !panel.CustomName.Contains(BlackScreensTag));
        if (hudPanels.Any())
        {
            var hudRenderer = new HudRenderer(hudPanels.First(), .5f, currentCommand, inputManager, customIcons, commandsForNonHover);
            views.Add(new PanelView(Me.CubeGrid, hudData, hudPanels.ToList(), inputManager, currentCommand, hudRenderer));
        }
        
        var remoteControls = new List<IMyRemoteControl>();
        hardwareGroup.GetBlocksOfType(remoteControls);
        remoteControl = remoteControls.FirstOrDefault();
        
        var blackScreen = textPanels.FirstOrDefault(panel => panel.CustomName.Contains(BlackScreensTag));
        var cockpitController = shipControllers.FirstOrDefault(controller => controller is IMyTextSurfaceProvider);
        
        if (cockpitController != null)
        {
            var surface = blackScreen ?? ((IMyTextSurfaceProvider)cockpitController).GetSurface(0);
            var cameraController = new CameraController(cockpitController);
            var hudRenderer = new HudRenderer(surface, .5f, currentCommand, inputManager, customIcons, commandsForNonHover);
            views.Add(new CockpitView(inputManager, cameraController, hudData, surface, currentCommand, hudRenderer));
        }
        
        hardwareGroup.GetBlocksOfType(sensors);
        sensors.ForEach(sensor =>
        {
            sensor.DetectPlayers = true;
            sensor.DetectOwner = true;
        });
        
        hardwareGroup.GetBlocksOfType(cameras);
        cameras.ForEach(camera =>
        {
            camera.EnableRaycast = true;
        });
    }
    
    ToggleManager.Initialize(new Dictionary<string, bool> { { "control", false }, { "render-inactive-view", true } }, toggleKey =>
    {
        switch (toggleKey)
        {
            case "control":
                if (ToggleManager.Instance.GetState("control"))
                {
                    inputManager.GetCurrentController().SetValueBool("ControlGyros", false);
                    IGC.SendBroadcastMessage("apck.command", "toggle:ignore-user-thruster:true", TransmissionDistance.CurrentConstruct);
                }
                else
                {
                    inputManager.GetCurrentController().SetValueBool("ControlGyros", true);
                    IGC.SendBroadcastMessage("apck.command", "toggle:ignore-user-thruster:false", TransmissionDistance.CurrentConstruct);
                }
                break;
            case "render-inactive-view":
                views.ForEach(view => view.ClearDisplay());
                break;
        }
    });
    
    this.commandProcessor = new CommandProcessor(new Dictionary<string, Action<string[]>> {
        {"set-value", (args) => Variables.Set(args[2], args[3])},
        {"cycle-view", (args) => CycleView()},
        {"commit", (args) => {
            foreach(var endpointId in hudData.selectedUnitIds) {
                if(IGC.IsEndpointReachable(endpointId))
                    IGC.SendUnicastMessage(endpointId, "apck.command", $"command:signal");
            }
        }},
        {"cast", (args) => {
            var view = views.ElementAt(currentViewIndex);
            view.CastRay(cameras, hitInfo => {
                var staticTarget = CreateStaticTarget(-1, hitInfo.EntityId, hitInfo.Name, hitInfo.HitPosition.Value, Vector3D.Zero);
                hudData.persistentProjections.Add(staticTarget);
            });
        }}
    });
}

// Helper method to add items to collections safely
static void AddSafely<T>(T item, IList<T> collection) where T : class
{
    if ((item != null) && !collection.Contains(item)) collection.Add(item);
}

// Generic method to find blocks
T FindBlock<T>(Func<IMyTerminalBlock, bool> predicate) where T : class
{
    var blocks = new List<IMyTerminalBlock>();
    GridTerminalSystem.GetBlocksOfType(blocks, block => ((block is T) && predicate(block)));
    return blocks.First() as T;
}

Program()
{
    Runtime.UpdateFrequency = UpdateFrequency.Update1;
    Initialize();
}

class InteractableObject
{
    public bool isSelectable;
    public ProjectionData projectionData;
    public List<string> availableCommands { get; private set; }
    public string defaultCommand;
    public InteractableObject(ProjectionData data, List<string> commands, string defaultCmd, bool selectable)
    {
        projectionData = data;
        availableCommands = commands;
        defaultCommand = defaultCmd;
        isSelectable = selectable;
    }
    public long sourceId;
    public long targetId;
    public Func<ProjectionData, ProjectionData> onHover;
    public Func<ProjectionData, ProjectionData> onSelected;
    public Action<HudRenderer, RectangleF, Vector2> onContextMenu;
    public Action<Vector3D, string, InteractableObject> onCommand;
    public Action onActivate;
}

struct ProjectionData
{
    public Vector3D worldPosition;
    public Vector3D velocity;
    public string spriteName;
    public List<MySprite> customSprites;
    public Vector2 size;
    public Color color;
    public float rotation;
    public string text;
    public float scale;
    public InteractableObject interactable;
}

// IGC message processing methods
IEnumerable<MyTuple<T, long>> GetBroadcastMessages<T>(IMyIntergridCommunicationSystem igc, string tag)
{
    var listener = igc.RegisterBroadcastListener(tag);
    while (listener.HasPendingMessage)
    {
        var message = listener.AcceptMessage();
        yield return new MyTuple<T, long>((T)message.Data, message.Source);
    }
}

ProjectionData CreateStaticTarget(long sourceId, long entityId, string name, Vector3D position, Vector3D velocity)
{
    var target = new ProjectionData();
    target.worldPosition = position;
    target.color = Color.Aqua;
    target.spriteName = @"SquareHollow";
    target.size = new Vector2(15, 15);
    target.rotation = (float)Math.PI / 4f;
    target.text = "static:" + name;
    target.scale = 1.5f;
    target.interactable = new InteractableObject(target, unitToStaticTargetCommands, "attack-pos", false);
    target.interactable.targetId = entityId;
    target.interactable.onHover = data => { data.rotation = runCount / 100f; data.text = "go"; return data; };
    target.interactable.onActivate = () =>
    {
        foreach (var unit in hudData.GetSelectedUnits())
        {
            if (unitToStaticTargetCommands.Contains(currentCommand.selectedCommand))
                unit.interactable.onCommand?.Invoke(position, currentCommand.selectedCommand, target.interactable);
            else
                unit.interactable.onCommand?.Invoke(position, target.interactable.defaultCommand, target.interactable);
        }
    };
    return target;
}

void ProcessGridSenseData()
{
    foreach (var message in GetBroadcastMessages<MyTuple<MyTuple<string, long, long, byte, byte>, Vector3D, Vector3D, MatrixD, BoundingBoxD>>(IGC, "tgp.global.gridsense.update"))
    {
        var sourceId = message.Item2;
        var data = message.Item1;
        var entityInfo = data.Item1;
        var position = data.Item2;
        var velocity = data.Item3;
        var faction = data.Item4;
        var relation = data.Item5;

        var target = new ProjectionData();
        target.worldPosition = position;
        target.color = Color.Red;
        target.spriteName = @"SquareHollow";
        target.size = new Vector2(15, 15);
        target.rotation = (float)Math.PI / 4f;
        target.text = "tgp.global:" + entityInfo.Item1;
        target.scale = 1.5f;
        var entityId = entityInfo.Item2;
        target.interactable = new InteractableObject(target, unitToDatalinkTargetCommands, "attack", false);
        target.interactable.targetId = entityId;
        target.interactable.onHover = data => { data.rotation = runCount / 100f; data.text = "attack"; return data; };
        target.interactable.onActivate = () =>
        {
            foreach (var unit in hudData.GetSelectedUnits())
            {
                if (unitToDatalinkTargetCommands.Contains(currentCommand.selectedCommand))
                    unit.interactable.onCommand?.Invoke(position, currentCommand.selectedCommand, target.interactable);
                else
                    unit.interactable.onCommand?.Invoke(position, target.interactable.defaultCommand, target.interactable);
            }
        };
        hudData.frameProjections.Add(target);
    }
}

void ProcessTaskData()
{
    foreach (var message in GetBroadcastMessages<ImmutableArray<MyTuple<int, string, Vector3D>>>(IGC, "captain-commander.task-data"))
    {
        var controller = inputManager.GetCurrentController();
        var taskCount = message.Item1.Length;
        if (taskCount > 1)
        {
            int verticalOffset = 0;
            for (int i = 1; i < taskCount; i++)
            {
                var task = message.Item1[i];
                var taskPosition = task.Item3;
                if (message.Item1[i - 1].Item3 != taskPosition)
                {
                    verticalOffset = 0;
                    hudData.frameLines.Add(new MyTuple<Vector3D, Vector3D, Vector4>(message.Item1[i - 1].Item3, taskPosition, Color.PaleGreen.ToVector4()));
                }
                else
                {
                    if (controller != null)
                    {
                        double distance;
                        var cockpitView = views.ElementAt(currentViewIndex) as CockpitView;
                        if (cockpitView != null)
                            distance = (cockpitView.GetCameraPosition() - taskPosition).Length();
                        else
                            distance = (controller.WorldMatrix.Translation - taskPosition).Length();
                        verticalOffset++;
                        taskPosition = taskPosition + controller.WorldMatrix.Down * verticalOffset * (distance / 50f);
                    }
                }

                var taskProjection = new ProjectionData();
                var taskName = task.Item2;
                if (customIcons.ContainsKey(taskName))
                    taskProjection.customSprites = customIcons[taskName].GetSprites(Vector2.Zero, new Vector2(15, 15));
                taskProjection.spriteName = @"Textures\FactionLogo\Builders\BuilderIcon_2.dds";
                taskProjection.worldPosition = taskPosition;
                taskProjection.color = Color.Green;
                taskProjection.size = new Vector2(15, 15);
                taskProjection.text = task.Item2;
                taskProjection.scale = 1.5f;
                taskProjection.interactable = new InteractableObject(taskProjection, unitToTaskCommands, "remove", false);
                taskProjection.interactable.targetId = task.Item1;
                taskProjection.interactable.onHover = data => { data.customSprites = null; data.color = Color.Goldenrod; data.size = new Vector2(30, 30); data.rotation = runCount / 100f; data.text = "delete"; return data; };
                taskProjection.interactable.onActivate = () =>
                {
                    logger.Info($"Remove task {task.Item1} command for {message.Item2}");
                    IGC.SendUnicastMessage(message.Item2, "apck.command", $"command:remove-task:{task.Item1}");
                };
                hudData.frameProjections.Add(taskProjection);
            }
        }
    }
}

void ProcessUnitReports()
{
    foreach (var message in GetBroadcastMessages<MyTuple<MyTuple<string, byte, int, long>, Vector3D, Vector3D, Vector3D, string>>(IGC, "apck.report"))
    {
        var data = message.Item1;
        var unitInfo = data.Item1;
        var unitName = unitInfo.Item1;
        var faction = unitInfo.Item2;
        var relation = unitInfo.Item3;
        var position = data.Item2;
        var velocity = data.Item3;
        var target = data.Item4;
        var status = data.Item5;

        var unitProjection = new ProjectionData();
        unitProjection.worldPosition = position;
        unitProjection.color = Color.Green;
        if (!string.IsNullOrEmpty(unitName))
            unitProjection.text = unitName;
        unitProjection.scale = 0.7f;
        unitProjection.spriteName = "Triangle";
        unitProjection.size = new Vector2(15, 15);
        unitProjection.rotation = 3.14f;
        var sourceId = message.Item2;
        unitProjection.interactable = new InteractableObject(unitProjection, unitToUnitCommands, "follow", true);
        unitProjection.interactable.targetId = sourceId;
        unitProjection.interactable.onHover = data => { data.spriteName = "SquareHollow"; data.color = Color.Goldenrod; data.size = new Vector2(30, 30); data.rotation = runCount / 100f; data.text = status; return data; };
        unitProjection.interactable.onSelected = data => { data.color = Color.DarkSeaGreen; data.color.A = 100; data.text = status; data.scale = 2f; return data; };
        unitProjection.interactable.sourceId = message.Item2;
        unitProjection.interactable.onActivate = () =>
        {
            foreach (var selectedUnit in hudData.GetSelectedUnits())
            {
                if (unitToUnitCommands.Contains(currentCommand.selectedCommand))
                    selectedUnit.interactable.onCommand?.Invoke(position, currentCommand.selectedCommand, unitProjection.interactable);
                else
                    selectedUnit.interactable.onCommand?.Invoke(position, unitProjection.interactable.defaultCommand, unitProjection.interactable);
            }
        };

        unitProjection.interactable.onCommand = (worldPos, command, targetObj) =>
        {
            if (hudData.selectionCenter != null)
                worldPos = worldPos - (hudData.selectionCenter.Value - unitProjection.worldPosition);
            logger.Info($"Got a click while selected {sourceId}. Hovered obj: {targetObj?.targetId}. Cmd tag: {command}");
            command = tasksMapping[command];
            if (targetObj != null)
                command = command.Replace("{targetId}", targetObj.targetId.ToString()).Replace("{wPos}", FormatWorldPosition(targetObj.projectionData.worldPosition));
            else
                command = command.Replace("{wPos}", FormatWorldPosition(worldPos));
            logger.Info($"Sending cmd '{command}'");
            IGC.SendUnicastMessage(unitProjection.interactable.sourceId, "apck.command", command);
        };

        hudData.frameProjections.Add(unitProjection);

        var velocityLength = velocity.Length();
        if (velocityLength > 1)
        {
            var velocityProjection = new ProjectionData();
            velocityProjection.worldPosition = position + velocity;
            velocityProjection.color = Color.Green;
            velocityProjection.scale = 0.7f;
            velocityProjection.spriteName = "Circle";
            velocityProjection.size = new Vector2(5, 5);
            velocityProjection.text = $"\nP{velocityLength:f1}";
            hudData.frameProjections.Add(velocityProjection);

            var lineColor = Color.SeaGreen;
            lineColor.A = 40;
            hudData.frameLines.Add(new MyTuple<Vector3D, Vector3D, Vector4>(position, position + velocity, lineColor.ToVector4()));

            if (target != Vector3D.Zero)
            {
                var targetProjection = new ProjectionData();
                targetProjection.worldPosition = target;
                targetProjection.color = Color.Green;
                targetProjection.scale = 0.7f;
                targetProjection.spriteName = "CircleHollow";
                targetProjection.size = new Vector2(10, 10) * (2 + (float)Math.Sin(runCount / 30f) * 0.5f);
                hudData.frameProjections.Add(targetProjection);
            }
        }
    }
}

// Utility method to format world position
string FormatWorldPosition(Vector3D position)
{
    return $"{position.X:F1},{position.Y:F1},{position.Z:F1}";
}

// Method to cycle through views
void CycleView()
{
    if (++currentViewIndex > views.Count - 1) currentViewIndex = 0;
    views.ForEach(view => view.ClearDisplay());
}
